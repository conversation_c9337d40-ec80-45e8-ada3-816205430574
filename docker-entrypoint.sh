#!/bin/bash

# Docker entrypoint script for AMP Proxy
# Handles virtual display setup for <PERSON><PERSON> in headless environments

set -e

# Function to start virtual display
start_virtual_display() {
    echo "Starting virtual display for Playwright..."
    
    # Start Xvfb (X Virtual Framebuffer)
    export DISPLAY=:99
    Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
    XVFB_PID=$!
    
    # Wait for Xvfb to start
    sleep 2
    
    # Start window manager
    fluxbox -display :99 &
    FLUXBOX_PID=$!
    
    # Wait for window manager to start
    sleep 1
    
    echo "Virtual display started on :99"
    
    # Store PIDs for cleanup
    echo $XVFB_PID > /tmp/xvfb.pid
    echo $FLUXBOX_PID > /tmp/fluxbox.pid
}

# Function to cleanup virtual display
cleanup_virtual_display() {
    echo "Cleaning up virtual display..."
    
    if [ -f /tmp/fluxbox.pid ]; then
        kill $(cat /tmp/fluxbox.pid) 2>/dev/null || true
        rm -f /tmp/fluxbox.pid
    fi
    
    if [ -f /tmp/xvfb.pid ]; then
        kill $(cat /tmp/xvfb.pid) 2>/dev/null || true
        rm -f /tmp/xvfb.pid
    fi
}

# Trap cleanup on exit
trap cleanup_virtual_display EXIT

# Check if we need virtual display (when not in headless mode)
if [ "${AUTO_REGISTRATION_HEADLESS:-true}" = "false" ]; then
    echo "Non-headless mode detected, starting virtual display..."
    start_virtual_display
else
    echo "Headless mode enabled, skipping virtual display setup"
fi

# Set environment variables for Playwright
export PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright

# Ensure log directory exists
mkdir -p /app/logs

# Start the application
echo "Starting AMP Proxy application..."
exec python run.py "$@"
