"""
Key-related utility functions.

This module contains utility functions for working with API keys,
including masking, validation, and formatting.
"""

import re
from typing import Optional


def mask_key(key: str, visible_chars: int = 4, mask_char: str = "*") -> str:
    """
    Mask an API key for safe logging and display.
    
    Args:
        key: The API key to mask
        visible_chars: Number of characters to show at the end
        mask_char: Character to use for masking
        
    Returns:
        Masked key string
        
    Examples:
        >>> mask_key("sk-1234567890abcdef")
        "...cdef"
        >>> mask_key("short", visible_chars=2)
        "**"
        >>> mask_key("sk-1234567890abcdef", visible_chars=6)
        "...90abcdef"
    """
    if not key:
        return mask_char * 4
    
    if len(key) <= visible_chars:
        return mask_char * len(key)
    
    return f"...{key[-visible_chars:]}"


def validate_key_format(key: str, min_length: int = 8, max_length: int = 200) -> bool:
    """
    Validate the format of an API key.
    
    Args:
        key: The API key to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        
    Returns:
        True if key format is valid, False otherwise
        
    Examples:
        >>> validate_key_format("sk-1234567890abcdef")
        True
        >>> validate_key_format("short")
        False
        >>> validate_key_format("")
        False
    """
    if not key or not isinstance(key, str):
        return False
    
    # Check length
    if len(key) < min_length or len(key) > max_length:
        return False
    
    # Check for whitespace
    if key != key.strip():
        return False
    
    # Check for basic printable characters only
    if not key.isprintable():
        return False
    
    return True


def normalize_key(key: str) -> str:
    """
    Normalize an API key by stripping whitespace and ensuring consistent format.
    
    Args:
        key: The API key to normalize
        
    Returns:
        Normalized key string
        
    Raises:
        ValueError: If the key is invalid after normalization
        
    Examples:
        >>> normalize_key("  sk-1234567890abcdef  ")
        "sk-1234567890abcdef"
        >>> normalize_key("")
        Traceback (most recent call last):
        ...
        ValueError: Key cannot be empty after normalization
    """
    if not key:
        raise ValueError("Key cannot be empty")
    
    normalized = key.strip()
    
    if not normalized:
        raise ValueError("Key cannot be empty after normalization")
    
    if not validate_key_format(normalized):
        raise ValueError("Key format is invalid after normalization")
    
    return normalized


def extract_key_suffix(key: str, suffix_length: int = 4) -> str:
    """
    Extract the suffix (last N characters) from an API key.
    
    Args:
        key: The API key
        suffix_length: Number of characters to extract from the end
        
    Returns:
        Key suffix
        
    Examples:
        >>> extract_key_suffix("sk-1234567890abcdef")
        "cdef"
        >>> extract_key_suffix("short", 2)
        "rt"
        >>> extract_key_suffix("ab", 5)
        "ab"
    """
    if not key:
        return ""
    
    return key[-suffix_length:] if len(key) >= suffix_length else key


def is_key_similar(key1: str, key2: str, suffix_length: int = 4) -> bool:
    """
    Check if two keys are similar based on their suffixes.
    
    Args:
        key1: First API key
        key2: Second API key
        suffix_length: Length of suffix to compare
        
    Returns:
        True if keys have the same suffix, False otherwise
        
    Examples:
        >>> is_key_similar("sk-1234567890abcdef", "different-prefix-cdef")
        True
        >>> is_key_similar("sk-1234567890abcdef", "sk-1234567890xyz")
        False
    """
    if not key1 or not key2:
        return False
    
    suffix1 = extract_key_suffix(key1, suffix_length)
    suffix2 = extract_key_suffix(key2, suffix_length)
    
    return suffix1 == suffix2


def generate_key_id(key: str) -> str:
    """
    Generate a unique identifier for a key based on its content.
    
    Args:
        key: The API key
        
    Returns:
        Unique identifier string
        
    Examples:
        >>> generate_key_id("sk-1234567890abcdef")
        "key_cdef"
        >>> generate_key_id("short")
        "key_short"
    """
    if not key:
        return "key_empty"
    
    suffix = extract_key_suffix(key, 4)
    return f"key_{suffix}"


def sanitize_key_for_logging(key: str) -> str:
    """
    Sanitize a key for safe logging by masking sensitive parts.
    
    This function is specifically designed for logging purposes and
    provides a consistent format for key representation in logs.
    
    Args:
        key: The API key to sanitize
        
    Returns:
        Sanitized key string safe for logging
        
    Examples:
        >>> sanitize_key_for_logging("sk-1234567890abcdef")
        "KEY(...cdef)"
        >>> sanitize_key_for_logging("")
        "KEY(empty)"
        >>> sanitize_key_for_logging("short")
        "KEY(****)"
    """
    if not key:
        return "KEY(empty)"
    
    masked = mask_key(key)
    return f"KEY({masked})"


def validate_client_key_format(key: str) -> bool:
    """
    Validate the format of a client API key.
    
    Client keys may have different format requirements than upstream keys.
    
    Args:
        key: The client API key to validate
        
    Returns:
        True if key format is valid, False otherwise
        
    Examples:
        >>> validate_client_key_format("sk-client-123")
        True
        >>> validate_client_key_format("short")
        False
    """
    # For now, use the same validation as regular keys
    # This can be customized later if client keys have different requirements
    return validate_key_format(key, min_length=6, max_length=100)


def validate_upstream_key_format(key: str) -> bool:
    """
    Validate the format of an upstream API key.
    
    Upstream keys may have specific format requirements for the target API.
    
    Args:
        key: The upstream API key to validate
        
    Returns:
        True if key format is valid, False otherwise
        
    Examples:
        >>> validate_upstream_key_format("sgamp_user_01JYKDQX9Z2VMT6ND78S86RQPV_b40b5f923794462f70e88722a6f2ed3387c0837dd20e155d396e32b834b8402c")
        True
        >>> validate_upstream_key_format("short")
        False
    """
    if not validate_key_format(key, min_length=20, max_length=300):
        return False
    
    # Additional validation for upstream keys can be added here
    # For example, checking for specific prefixes or patterns
    
    return True


def compare_key_suffixes(key: str, suffix: str) -> bool:
    """
    Compare a key's suffix with a given suffix.
    
    Args:
        key: The full API key
        suffix: The suffix to compare against
        
    Returns:
        True if the key ends with the given suffix, False otherwise
        
    Examples:
        >>> compare_key_suffixes("sk-1234567890abcdef", "cdef")
        True
        >>> compare_key_suffixes("sk-1234567890abcdef", "xyz")
        False
    """
    if not key or not suffix:
        return False
    
    return key.endswith(suffix)
