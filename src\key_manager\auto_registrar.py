"""
Automatic key registration module.

This module provides functionality to automatically register new accounts
and obtain API keys when the number of active keys falls below a threshold.
"""

import asyncio
import json
import logging
import os
import random
import re
import string
import time
import urllib.parse
from typing import Dict, List, Optional

import requests
from faker import Faker
from playwright.async_api import async_playwright

from ..models import Upstream<PERSON>ey
from ..utils import get_logger, sanitize_key_for_logging

logger = get_logger(__name__)


class AutoRegistrar:
    """
    Automatic key registration service.
    
    This class handles the automatic registration of new accounts
    to obtain fresh API keys when needed.
    """
    
    def __init__(self, min_keys: int = 3, max_registration_attempts: int = 5):
        """
        Initialize the auto registrar.
        
        Args:
            min_keys: Minimum number of active keys to maintain
            max_registration_attempts: Maximum registration attempts per session
        """
        self.min_keys = min_keys
        self.max_registration_attempts = max_registration_attempts
        self.fake = Faker()
        self.accounts_file = "registered_accounts.json"
        self.base_url = "https://mail.hiclover.me"
        
    def generate_random_email(self) -> str:
        """Generate a random temporary email address."""
        prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        domain = 'mail.hiclover.me'
        email = f"{prefix}@{domain}"
        logger.info(f"Generated temporary email: {sanitize_key_for_logging(email)}")
        return email
    
    def get_mail_list(self, mailbox: str) -> List[Dict]:
        """
        Get mail list for a given mailbox.
        
        Args:
            mailbox: Email address to check
            
        Returns:
            List of emails
        """
        try:
            mailbox_encoded = urllib.parse.quote(mailbox)
            response = requests.get(f"{self.base_url}/api/mails/{mailbox_encoded}")
            response.raise_for_status()
            return response.json().get('mails', [])
        except requests.RequestException as e:
            logger.warning(f"Failed to get mail list for {sanitize_key_for_logging(mailbox)}: {e}")
            return []
    
    async def get_verification_code(self, mailbox: str, timeout: int = 90) -> Optional[str]:
        """
        Get verification code from email.
        
        Args:
            mailbox: Email address to check
            timeout: Timeout in seconds
            
        Returns:
            Verification code if found, None otherwise
        """
        logger.info(f"Checking mailbox {sanitize_key_for_logging(mailbox)} for verification code...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            mails = self.get_mail_list(mailbox)
            if mails:
                latest_mail = mails[0]
                mail_subject = latest_mail.get('subject', '').lower()
                
                if 'verify' in mail_subject and 'email' in mail_subject:
                    mail_body = latest_mail.get('text', '') + latest_mail.get('html', '')
                    match = re.search(r'\b(\d{6})\b', mail_body)
                    if match:
                        code = match.group(1)
                        logger.info(f"Successfully extracted verification code: {code}")
                        return code
            
            await asyncio.sleep(5)
        
        logger.warning("Verification code retrieval timeout!")
        return None
    
    def save_account_info(self, account_data: Dict) -> None:
        """
        Save account information to JSON file.
        
        Args:
            account_data: Account information to save
        """
        all_accounts = []
        
        if os.path.exists(self.accounts_file) and os.path.getsize(self.accounts_file) > 0:
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    all_accounts = json.load(f)
            except json.JSONDecodeError:
                all_accounts = []
        
        all_accounts.append(account_data)
        
        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(all_accounts, f, indent=4, ensure_ascii=False)
        
        logger.info(f"Account information saved to {self.accounts_file}")
    
    async def register_single_account(self) -> Optional[Dict]:
        """
        Register a single new account and extract API key.
        
        Returns:
            Account information if successful, None otherwise
        """
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        email = self.generate_random_email()
        password = ''.join(random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=16
        ))
        
        logger.info(f"Starting registration for: {first_name} {last_name}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)  # Run headless for automation
            page = await browser.new_page()
            
            try:
                logger.info("Opening registration page...")
                await page.goto("https://auth.ampcode.com/sign-up", timeout=60000)
                
                # Fill registration form
                await page.locator('input[name="first_name"]').fill(first_name)
                await page.locator('input[name="last_name"]').fill(last_name)
                await page.locator('input[name="email"]').fill(email)
                await page.locator('button[type="submit"]').click()
                
                # Set password
                logger.info("Setting password...")
                await page.locator('input[name="password"]').wait_for(timeout=10000)
                await page.locator('input[name="password"]').fill(password)
                await page.locator('button[type="submit"]').click()
                
                # Check for registration restrictions
                logger.info("Checking registration result...")
                success_selector = 'input[inputmode="numeric"]'
                failure_selector = '*:has-text("Sign up is restricted")'
                combined_selector = f"{success_selector}, {failure_selector}"
                
                await page.wait_for_selector(combined_selector, timeout=15000)
                
                if await page.locator(failure_selector).is_visible():
                    raise Exception("Registration restricted by website")
                
                logger.info("Successfully reached email verification page")
                
                # Get verification code
                logger.info("Verifying email...")
                first_code_input = page.locator(success_selector).first
                code = await self.get_verification_code(email)
                
                if not code:
                    raise Exception("Failed to get verification code")
                
                await first_code_input.type(code, delay=50)
                logger.info("Waiting for registration completion...")
                await page.wait_for_url("https://ampcode.com/**", timeout=20000)
                logger.info("Registration successful! Extracting API key...")
                
                # Extract API key
                await page.goto("https://ampcode.com/settings")
                api_key_locator = page.locator(
                    "xpath=//*[contains(text(), 'amp_')] | //*[contains(@value, 'amp_')]"
                ).first
                await api_key_locator.wait_for(timeout=20000)
                
                raw_text = ""
                try:
                    raw_text = await api_key_locator.input_value()
                except Exception:
                    raw_text = await api_key_locator.inner_text()
                
                match = re.search(r'((sg)?amp_[a-zA-Z0-9_]+)', raw_text)
                
                if match:
                    api_key = match.group(1).strip()
                    await browser.close()
                    
                    account_info = {
                        "first_name": first_name,
                        "last_name": last_name,
                        "email": email,
                        "password": password,
                        "api_key": api_key,
                        "registered_at": time.time()
                    }
                    
                    logger.info(f"Successfully registered account with API key: {sanitize_key_for_logging(api_key)}")
                    return account_info
                else:
                    raise Exception(f"Failed to extract API key, raw text: '{raw_text}'")
                    
            except Exception as e:
                logger.error(f"Registration failed: {e}")
                await page.screenshot(path=f"error_screenshot_{email.split('@')[0]}.png")
                await browser.close()
                return None
    
    async def register_multiple_keys(self, count: int) -> List[UpstreamKey]:
        """
        Register multiple new accounts and return UpstreamKey objects.
        
        Args:
            count: Number of keys to register
            
        Returns:
            List of new UpstreamKey objects
        """
        new_keys = []
        successful_registrations = 0
        
        logger.info(f"Starting registration of {count} new accounts...")
        
        for i in range(min(count, self.max_registration_attempts)):
            logger.info(f"Registration attempt {i + 1}/{count}")
            
            account_info = await self.register_single_account()
            
            if account_info:
                # Save account info
                self.save_account_info(account_info)
                
                # Create UpstreamKey object
                upstream_key = UpstreamKey(
                    key=account_info["api_key"],
                    weight=1,
                    enabled=True,
                    description=f"Auto-registered key for {account_info['first_name']} {account_info['last_name']}"
                )
                
                new_keys.append(upstream_key)
                successful_registrations += 1
                
                logger.info(f"Successfully registered key {i + 1}/{count}")
            else:
                logger.warning(f"Failed to register key {i + 1}/{count}")
            
            # Add delay between registrations
            if i < count - 1:
                delay = 10 if not account_info else 5
                logger.info(f"Waiting {delay} seconds before next registration...")
                await asyncio.sleep(delay)
        
        logger.info(f"Registration completed: {successful_registrations}/{count} successful")
        return new_keys
    
    def should_register_new_keys(self, active_keys_count: int) -> bool:
        """
        Check if new keys should be registered.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            True if new keys should be registered
        """
        return active_keys_count < self.min_keys
    
    def calculate_keys_needed(self, active_keys_count: int) -> int:
        """
        Calculate how many new keys are needed.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            Number of keys to register
        """
        if active_keys_count >= self.min_keys:
            return 0
        
        return self.min_keys - active_keys_count
