"""
Automatic key registration module.

This module provides functionality to automatically register new accounts
and obtain API keys when the number of active keys falls below a threshold.
"""

import asyncio
import json
import logging
import os
import random
import re
import string
import time
import urllib.parse
from typing import Dict, List, Optional

import requests
from faker import Faker
from playwright.async_api import async_playwright

from ..models import Upstream<PERSON>ey
from ..utils import get_logger, sanitize_key_for_logging
from ..utils.cf_bypass import CloudflareBypass

logger = get_logger(__name__)


class AutoRegistrar:
    """
    Automatic key registration service.
    
    This class handles the automatic registration of new accounts
    to obtain fresh API keys when needed.
    """
    
    def __init__(self, min_keys: int = 3, max_registration_attempts: int = 5, headless: bool = False):
        """
        Initialize the auto registrar.

        Args:
            min_keys: Minimum number of active keys to maintain
            max_registration_attempts: Maximum registration attempts per session
            headless: Whether to run browser in headless mode
        """
        self.min_keys = min_keys
        self.max_registration_attempts = max_registration_attempts
        self.headless = headless
        self.fake = Faker()
        self.accounts_file = "registered_accounts.json"
        self.base_url = "https://mail.hiclover.me"
        self.cf_bypass = CloudflareBypass()
        
    def generate_random_email(self) -> str:
        """Generate a random temporary email address."""
        prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        domain = 'mail.hiclover.me'
        email = f"{prefix}@{domain}"
        logger.info(f"Generated temporary email: {sanitize_key_for_logging(email)}")
        return email
    
    def get_mail_list(self, mailbox: str) -> List[Dict]:
        """
        Get mail list for a given mailbox.
        
        Args:
            mailbox: Email address to check
            
        Returns:
            List of emails
        """
        try:
            mailbox_encoded = urllib.parse.quote(mailbox)
            response = requests.get(f"{self.base_url}/api/mails/{mailbox_encoded}")
            response.raise_for_status()
            return response.json().get('mails', [])
        except requests.RequestException as e:
            logger.warning(f"Failed to get mail list for {sanitize_key_for_logging(mailbox)}: {e}")
            return []
    
    async def get_verification_code(self, mailbox: str, timeout: int = 90) -> Optional[str]:
        """
        Get verification code from email.
        
        Args:
            mailbox: Email address to check
            timeout: Timeout in seconds
            
        Returns:
            Verification code if found, None otherwise
        """
        logger.info(f"Checking mailbox {sanitize_key_for_logging(mailbox)} for verification code...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            mails = self.get_mail_list(mailbox)
            if mails:
                latest_mail = mails[0]
                mail_subject = latest_mail.get('subject', '').lower()
                
                if 'verify' in mail_subject and 'email' in mail_subject:
                    mail_body = latest_mail.get('text', '') + latest_mail.get('html', '')
                    match = re.search(r'\b(\d{6})\b', mail_body)
                    if match:
                        code = match.group(1)
                        logger.info(f"Successfully extracted verification code: {code}")
                        return code
            
            await asyncio.sleep(5)
        
        logger.warning("Verification code retrieval timeout!")
        return None
    
    def save_account_info(self, account_data: Dict) -> None:
        """
        Save account information to JSON file.
        
        Args:
            account_data: Account information to save
        """
        all_accounts = []
        
        if os.path.exists(self.accounts_file) and os.path.getsize(self.accounts_file) > 0:
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    all_accounts = json.load(f)
            except json.JSONDecodeError:
                all_accounts = []
        
        all_accounts.append(account_data)
        
        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(all_accounts, f, indent=4, ensure_ascii=False)
        
        logger.info(f"Account information saved to {self.accounts_file}")
    
    async def register_single_account(self) -> Optional[Dict]:
        """
        Register a single new account and extract API key.

        Returns:
            Account information if successful, None otherwise
        """
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        email = self.generate_random_email()
        password = ''.join(random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=16
        ))

        logger.info(f"Starting registration for: {first_name} {last_name}")

        async with async_playwright() as p:
            # Use Cloudflare bypass for browser creation
            browser = await self.cf_bypass.create_stealth_browser(p, headless=self.headless)
            context = await self.cf_bypass.create_stealth_context(browser)
            page = await context.new_page()

            # Setup stealth page configurations
            await self.cf_bypass.setup_stealth_page(page)
            
            try:
                logger.info("Opening registration page with Cloudflare bypass...")

                # Use Cloudflare bypass navigation
                if not await self.cf_bypass.navigate_with_retry(page, "https://auth.ampcode.com/sign-up"):
                    raise Exception("Failed to navigate to registration page after multiple attempts")

                # Take screenshot for debugging
                await page.screenshot(path=f"debug_page_load_{int(time.time())}.png")
                logger.info("Page loaded successfully, took debug screenshot")

                # Fill registration form with human-like delays
                logger.info("Filling registration form...")

                # Try multiple selectors for first name input
                first_name_selectors = [
                    'input[name="first_name"]',
                    'input[name="firstName"]',
                    'input[placeholder*="First"]',
                    'input[id*="first"]',
                    'input[class*="first"]'
                ]

                first_name_input = None
                for selector in first_name_selectors:
                    try:
                        first_name_input = page.locator(selector)
                        await first_name_input.wait_for(timeout=15000)  # Increased timeout
                        logger.info(f"Found first name input with selector: {selector}")
                        break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        continue

                if not first_name_input:
                    # Take screenshot for debugging
                    await page.screenshot(path=f"debug_no_input_{int(time.time())}.png")
                    raise Exception("Could not find first name input field")

                # Use human-like typing
                await self.cf_bypass.human_like_typing(page, first_name_selectors[0], first_name)

                # Try multiple selectors for last name input
                last_name_selectors = [
                    'input[name="last_name"]',
                    'input[name="lastName"]',
                    'input[placeholder*="Last"]',
                    'input[id*="last"]',
                    'input[class*="last"]'
                ]

                last_name_input = None
                for selector in last_name_selectors:
                    try:
                        last_name_input = page.locator(selector)
                        await last_name_input.wait_for(timeout=15000)  # Increased timeout
                        logger.info(f"Found last name input with selector: {selector}")
                        break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        continue

                if not last_name_input:
                    raise Exception("Could not find last name input field")

                # Use human-like typing for last name
                await self.cf_bypass.human_like_typing(page, last_name_selectors[0], last_name)

                # Try multiple selectors for email input
                email_selectors = [
                    'input[name="email"]',
                    'input[type="email"]',
                    'input[placeholder*="email"]',
                    'input[id*="email"]',
                    'input[class*="email"]'
                ]

                email_input = None
                for selector in email_selectors:
                    try:
                        email_input = page.locator(selector)
                        await email_input.wait_for(timeout=15000)  # Increased timeout
                        logger.info(f"Found email input with selector: {selector}")
                        break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        continue

                if not email_input:
                    raise Exception("Could not find email input field")

                # Use human-like typing for email
                await self.cf_bypass.human_like_typing(page, email_selectors[0], email)

                # Submit form with human-like click
                await self.cf_bypass.human_like_click(page, 'button[type="submit"]')

                # Set password
                logger.info("Setting password...")
                password_input = page.locator('input[name="password"]')
                await password_input.wait_for(timeout=10000)

                # Use human-like typing for password
                await self.cf_bypass.human_like_typing(page, 'input[name="password"]', password)

                # Submit password
                password_submit = page.locator('button[type="submit"]')
                await password_submit.click()
                await asyncio.sleep(2)
                
                # Check for registration restrictions
                logger.info("Checking registration result...")
                success_selector = 'input[inputmode="numeric"]'
                failure_selector = 'div:has-text("Sign up is restricted")'

                # Wait a bit for the page to load
                await asyncio.sleep(3)

                try:
                    # Check if we reached the verification page
                    await page.wait_for_selector(success_selector, timeout=10000)
                    logger.info("Successfully reached email verification page")
                except:
                    # Check if registration was restricted
                    try:
                        restriction_element = page.locator(failure_selector)
                        if await restriction_element.is_visible():
                            raise Exception("Registration restricted by website")
                    except:
                        pass
                    raise Exception("Failed to reach verification page")

                # Get verification code
                logger.info("Verifying email...")
                code = await self.get_verification_code(email)

                if not code:
                    raise Exception("Failed to get verification code")

                # Handle 6-digit verification code input (6 separate input boxes)
                logger.info(f"Entering verification code: {code}")

                # Find the first input box and start typing
                # Based on your original code: first_code_input.type(code, delay=50)
                first_code_input = page.locator(success_selector).first
                await first_code_input.click()
                await asyncio.sleep(0.5)

                # Type the entire code with delay=50 like in original code
                # The website should automatically move focus between input boxes
                await first_code_input.type(code, delay=50)
                await asyncio.sleep(2)  # Wait for auto-submission or processing

                logger.info("Waiting for registration completion...")

                # Wait for navigation - be more flexible about the target URL
                registration_completed = False
                max_wait_time = 30  # seconds
                check_interval = 2  # seconds

                for attempt in range(max_wait_time // check_interval):
                    await asyncio.sleep(check_interval)
                    current_url = page.url

                    # Check if we've moved away from the auth domain
                    if "ampcode.com" in current_url and "auth.ampcode.com" not in current_url:
                        logger.info(f"Registration successful! Redirected to: {current_url}")
                        registration_completed = True
                        break

                    # Also check for specific success indicators on the page
                    try:
                        # Look for elements that indicate successful registration
                        success_indicators = [
                            "text=Welcome",
                            "text=Dashboard",
                            "text=Settings",
                            "text=API",
                            "[data-testid='dashboard']",
                            ".dashboard",
                            "#dashboard"
                        ]

                        for indicator in success_indicators:
                            if await page.locator(indicator).is_visible():
                                logger.info(f"Registration successful! Found success indicator: {indicator}")
                                registration_completed = True
                                break

                        if registration_completed:
                            break

                    except:
                        pass

                if not registration_completed:
                    current_url = page.url
                    logger.warning(f"Registration completion uncertain. Current URL: {current_url}")
                    # Continue anyway - maybe we can still extract the API key
                
                # Extract API key
                logger.info("Navigating to settings page...")

                # Try to navigate to settings, with fallback options
                settings_urls = [
                    "https://ampcode.com/settings",
                    "https://ampcode.com/dashboard/settings",
                    "https://ampcode.com/account/settings"
                ]

                settings_loaded = False
                for settings_url in settings_urls:
                    try:
                        await page.goto(settings_url, timeout=15000)
                        await asyncio.sleep(3)  # Wait for page to load

                        # Check if we're on a settings-like page
                        page_content = await page.content()
                        if any(keyword in page_content.lower() for keyword in ['api', 'key', 'token', 'settings']):
                            logger.info(f"Successfully loaded settings page: {settings_url}")
                            settings_loaded = True
                            break
                    except:
                        continue

                if not settings_loaded:
                    # Try to find settings link on current page
                    try:
                        settings_link = page.locator("a:has-text('Settings'), a:has-text('Account'), a:has-text('API')").first
                        await settings_link.click()
                        await asyncio.sleep(3)
                        logger.info("Clicked settings link")
                        settings_loaded = True
                    except:
                        logger.warning("Could not find settings page, trying to extract API key from current page")

                # Try multiple selectors to find the API key
                api_key_selectors = [
                    "xpath=//*[contains(text(), 'amp_')] | //*[contains(@value, 'amp_')]",
                    "input[value*='amp_']",
                    "textarea[value*='amp_']",
                    "*:has-text('amp_')",
                    "code:has-text('amp_')",
                    "pre:has-text('amp_')"
                ]

                api_key = None
                raw_text = ""

                for selector in api_key_selectors:
                    try:
                        api_key_locator = page.locator(selector).first
                        await api_key_locator.wait_for(timeout=5000)

                        # Try to get the value
                        try:
                            raw_text = await api_key_locator.input_value()
                        except:
                            try:
                                raw_text = await api_key_locator.inner_text()
                            except:
                                raw_text = await api_key_locator.text_content()

                        if raw_text:
                            match = re.search(r'((sg)?amp_[a-zA-Z0-9_]+)', raw_text)
                            if match:
                                api_key = match.group(1).strip()
                                break
                    except:
                        continue

                await browser.close()

                if api_key:
                    account_info = {
                        "first_name": first_name,
                        "last_name": last_name,
                        "email": email,
                        "password": password,
                        "api_key": api_key,
                        "registered_at": time.time()
                    }

                    logger.info(f"Successfully registered account with API key: {sanitize_key_for_logging(api_key)}")
                    return account_info
                else:
                    raise Exception(f"Failed to extract API key, raw text: '{raw_text}'")
                    
            except Exception as e:
                logger.error(f"Registration failed: {e}")
                try:
                    # Take screenshot for debugging
                    screenshot_path = f"error_screenshot_{email.split('@')[0]}_{int(time.time())}.png"
                    await page.screenshot(path=screenshot_path)
                    logger.info(f"Error screenshot saved: {screenshot_path}")
                except:
                    pass

                try:
                    await browser.close()
                except:
                    pass

                return None
    
    async def register_multiple_keys(self, count: int) -> List[UpstreamKey]:
        """
        Register multiple new accounts and return UpstreamKey objects.
        
        Args:
            count: Number of keys to register
            
        Returns:
            List of new UpstreamKey objects
        """
        new_keys = []
        successful_registrations = 0
        
        logger.info(f"Starting registration of {count} new accounts...")
        
        for i in range(min(count, self.max_registration_attempts)):
            logger.info(f"Registration attempt {i + 1}/{count}")
            
            account_info = await self.register_single_account()
            
            if account_info:
                # Save account info
                self.save_account_info(account_info)
                
                # Create UpstreamKey object
                upstream_key = UpstreamKey(
                    key=account_info["api_key"],
                    weight=1,
                    enabled=True,
                    description=f"Auto-registered key for {account_info['first_name']} {account_info['last_name']}"
                )
                
                new_keys.append(upstream_key)
                successful_registrations += 1
                
                logger.info(f"Successfully registered key {i + 1}/{count}")
            else:
                logger.warning(f"Failed to register key {i + 1}/{count}")
            
            # Add delay between registrations with randomization
            if i < count - 1:
                if not account_info:
                    # Longer delay after failure
                    delay = random.randint(15, 25)
                    logger.info(f"Registration failed, waiting {delay} seconds before retry...")
                else:
                    # Shorter delay after success, but still randomized
                    delay = random.randint(8, 15)
                    logger.info(f"Registration successful, waiting {delay} seconds before next attempt...")

                await asyncio.sleep(delay)
        
        logger.info(f"Registration completed: {successful_registrations}/{count} successful")
        return new_keys
    
    def should_register_new_keys(self, active_keys_count: int) -> bool:
        """
        Check if new keys should be registered.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            True if new keys should be registered
        """
        return active_keys_count < self.min_keys
    
    def calculate_keys_needed(self, active_keys_count: int) -> int:
        """
        Calculate how many new keys are needed.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            Number of keys to register
        """
        if active_keys_count >= self.min_keys:
            return 0
        
        return self.min_keys - active_keys_count
