"""
Automatic key registration module.

This module provides functionality to automatically register new accounts
and obtain API keys when the number of active keys falls below a threshold.
"""

import asyncio
import json
import logging
import os
import random
import re
import string
import time
import urllib.parse
from typing import Dict, List, Optional

import requests
from faker import Faker
from playwright.async_api import async_playwright

from ..models import Upstream<PERSON>ey
from ..utils import get_logger, sanitize_key_for_logging

logger = get_logger(__name__)


class AutoRegistrar:
    """
    Automatic key registration service.
    
    This class handles the automatic registration of new accounts
    to obtain fresh API keys when needed.
    """
    
    def __init__(self, min_keys: int = 3, max_registration_attempts: int = 5, headless: bool = False):
        """
        Initialize the auto registrar.

        Args:
            min_keys: Minimum number of active keys to maintain
            max_registration_attempts: Maximum registration attempts per session
            headless: Whether to run browser in headless mode
        """
        self.min_keys = min_keys
        self.max_registration_attempts = max_registration_attempts
        self.headless = headless
        self.fake = Faker()
        self.accounts_file = "registered_accounts.json"
        self.base_url = "https://mail.hiclover.me"
        
    def generate_random_email(self) -> str:
        """Generate a random temporary email address."""
        prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        domain = 'mail.hiclover.me'
        email = f"{prefix}@{domain}"
        logger.info(f"Generated temporary email: {sanitize_key_for_logging(email)}")
        return email
    
    def get_mail_list(self, mailbox: str) -> List[Dict]:
        """
        Get mail list for a given mailbox.
        
        Args:
            mailbox: Email address to check
            
        Returns:
            List of emails
        """
        try:
            mailbox_encoded = urllib.parse.quote(mailbox)
            response = requests.get(f"{self.base_url}/api/mails/{mailbox_encoded}")
            response.raise_for_status()
            return response.json().get('mails', [])
        except requests.RequestException as e:
            logger.warning(f"Failed to get mail list for {sanitize_key_for_logging(mailbox)}: {e}")
            return []
    
    async def get_verification_code(self, mailbox: str, timeout: int = 90) -> Optional[str]:
        """
        Get verification code from email.
        
        Args:
            mailbox: Email address to check
            timeout: Timeout in seconds
            
        Returns:
            Verification code if found, None otherwise
        """
        logger.info(f"Checking mailbox {sanitize_key_for_logging(mailbox)} for verification code...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            mails = self.get_mail_list(mailbox)
            if mails:
                latest_mail = mails[0]
                mail_subject = latest_mail.get('subject', '').lower()
                
                if 'verify' in mail_subject and 'email' in mail_subject:
                    mail_body = latest_mail.get('text', '') + latest_mail.get('html', '')
                    match = re.search(r'\b(\d{6})\b', mail_body)
                    if match:
                        code = match.group(1)
                        logger.info(f"Successfully extracted verification code: {code}")
                        return code
            
            await asyncio.sleep(5)
        
        logger.warning("Verification code retrieval timeout!")
        return None
    
    def save_account_info(self, account_data: Dict) -> None:
        """
        Save account information to JSON file.
        
        Args:
            account_data: Account information to save
        """
        all_accounts = []
        
        if os.path.exists(self.accounts_file) and os.path.getsize(self.accounts_file) > 0:
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    all_accounts = json.load(f)
            except json.JSONDecodeError:
                all_accounts = []
        
        all_accounts.append(account_data)
        
        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(all_accounts, f, indent=4, ensure_ascii=False)
        
        logger.info(f"Account information saved to {self.accounts_file}")
    
    async def register_single_account(self) -> Optional[Dict]:
        """
        Register a single new account and extract API key.

        Returns:
            Account information if successful, None otherwise
        """
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        email = self.generate_random_email()
        password = ''.join(random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=16
        ))

        logger.info(f"Starting registration for: {first_name} {last_name}")

        async with async_playwright() as p:
            # Use the same browser configuration as the original working code
            browser = await p.chromium.launch(
                headless=self.headless,  # Configurable headless mode
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--no-first-run',
                    '--disable-extensions',
                    '--disable-default-apps',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )

            # Create context with realistic user agent and viewport
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )

            page = await context.new_page()

            # Add stealth measures
            await page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                window.chrome = {
                    runtime: {},
                };
            """)
            
            try:
                logger.info("Opening registration page...")
                await page.goto("https://auth.ampcode.com/sign-up", timeout=60000)

                # Wait for page to fully load
                await asyncio.sleep(2)

                # Fill registration form with human-like delays
                logger.info("Filling registration form...")
                first_name_input = page.locator('input[name="first_name"]')
                await first_name_input.wait_for(timeout=10000)
                await first_name_input.click()
                await asyncio.sleep(0.5)
                await first_name_input.fill(first_name)
                await asyncio.sleep(0.3)

                last_name_input = page.locator('input[name="last_name"]')
                await last_name_input.click()
                await asyncio.sleep(0.5)
                await last_name_input.fill(last_name)
                await asyncio.sleep(0.3)

                email_input = page.locator('input[name="email"]')
                await email_input.click()
                await asyncio.sleep(0.5)
                await email_input.fill(email)
                await asyncio.sleep(0.5)

                # Submit form
                submit_button = page.locator('button[type="submit"]')
                await submit_button.click()
                await asyncio.sleep(2)

                # Set password
                logger.info("Setting password...")
                password_input = page.locator('input[name="password"]')
                await password_input.wait_for(timeout=10000)
                await password_input.click()
                await asyncio.sleep(0.5)
                await password_input.fill(password)
                await asyncio.sleep(0.5)

                # Submit password
                password_submit = page.locator('button[type="submit"]')
                await password_submit.click()
                await asyncio.sleep(2)
                
                # Check for registration restrictions
                logger.info("Checking registration result...")
                success_selector = 'input[inputmode="numeric"]'
                failure_selector = 'div:has-text("Sign up is restricted")'

                # Wait a bit for the page to load
                await asyncio.sleep(3)

                try:
                    # Check if we reached the verification page
                    await page.wait_for_selector(success_selector, timeout=10000)
                    logger.info("Successfully reached email verification page")
                except:
                    # Check if registration was restricted
                    try:
                        restriction_element = page.locator(failure_selector)
                        if await restriction_element.is_visible():
                            raise Exception("Registration restricted by website")
                    except:
                        pass
                    raise Exception("Failed to reach verification page")

                # Get verification code
                logger.info("Verifying email...")
                code = await self.get_verification_code(email)

                if not code:
                    raise Exception("Failed to get verification code")

                # Handle 6-digit verification code input (6 separate input boxes)
                logger.info(f"Entering verification code: {code}")

                # Find the first input box and start typing
                # Based on your original code: first_code_input.type(code, delay=50)
                first_code_input = page.locator(success_selector).first
                await first_code_input.click()
                await asyncio.sleep(0.5)

                # Type the entire code with delay=50 like in original code
                # The website should automatically move focus between input boxes
                await first_code_input.type(code, delay=50)
                await asyncio.sleep(2)  # Wait for auto-submission or processing

                logger.info("Waiting for registration completion...")

                # Wait for navigation - be more flexible about the target URL
                registration_completed = False
                max_wait_time = 30  # seconds
                check_interval = 2  # seconds

                for attempt in range(max_wait_time // check_interval):
                    await asyncio.sleep(check_interval)
                    current_url = page.url

                    # Check if we've moved away from the auth domain
                    if "ampcode.com" in current_url and "auth.ampcode.com" not in current_url:
                        logger.info(f"Registration successful! Redirected to: {current_url}")
                        registration_completed = True
                        break

                    # Also check for specific success indicators on the page
                    try:
                        # Look for elements that indicate successful registration
                        success_indicators = [
                            "text=Welcome",
                            "text=Dashboard",
                            "text=Settings",
                            "text=API",
                            "[data-testid='dashboard']",
                            ".dashboard",
                            "#dashboard"
                        ]

                        for indicator in success_indicators:
                            if await page.locator(indicator).is_visible():
                                logger.info(f"Registration successful! Found success indicator: {indicator}")
                                registration_completed = True
                                break

                        if registration_completed:
                            break

                    except:
                        pass

                if not registration_completed:
                    current_url = page.url
                    logger.warning(f"Registration completion uncertain. Current URL: {current_url}")
                    # Continue anyway - maybe we can still extract the API key
                
                # Extract API key
                logger.info("Navigating to settings page...")

                # Try to navigate to settings, with fallback options
                settings_urls = [
                    "https://ampcode.com/settings",
                    "https://ampcode.com/dashboard/settings",
                    "https://ampcode.com/account/settings"
                ]

                settings_loaded = False
                for settings_url in settings_urls:
                    try:
                        await page.goto(settings_url, timeout=15000)
                        await asyncio.sleep(3)  # Wait for page to load

                        # Check if we're on a settings-like page
                        page_content = await page.content()
                        if any(keyword in page_content.lower() for keyword in ['api', 'key', 'token', 'settings']):
                            logger.info(f"Successfully loaded settings page: {settings_url}")
                            settings_loaded = True
                            break
                    except:
                        continue

                if not settings_loaded:
                    # Try to find settings link on current page
                    try:
                        settings_link = page.locator("a:has-text('Settings'), a:has-text('Account'), a:has-text('API')").first
                        await settings_link.click()
                        await asyncio.sleep(3)
                        logger.info("Clicked settings link")
                        settings_loaded = True
                    except:
                        logger.warning("Could not find settings page, trying to extract API key from current page")

                # Try multiple selectors to find the API key
                api_key_selectors = [
                    "xpath=//*[contains(text(), 'amp_')] | //*[contains(@value, 'amp_')]",
                    "input[value*='amp_']",
                    "textarea[value*='amp_']",
                    "*:has-text('amp_')",
                    "code:has-text('amp_')",
                    "pre:has-text('amp_')"
                ]

                api_key = None
                raw_text = ""

                for selector in api_key_selectors:
                    try:
                        api_key_locator = page.locator(selector).first
                        await api_key_locator.wait_for(timeout=5000)

                        # Try to get the value
                        try:
                            raw_text = await api_key_locator.input_value()
                        except:
                            try:
                                raw_text = await api_key_locator.inner_text()
                            except:
                                raw_text = await api_key_locator.text_content()

                        if raw_text:
                            match = re.search(r'((sg)?amp_[a-zA-Z0-9_]+)', raw_text)
                            if match:
                                api_key = match.group(1).strip()
                                break
                    except:
                        continue

                await browser.close()

                if api_key:
                    account_info = {
                        "first_name": first_name,
                        "last_name": last_name,
                        "email": email,
                        "password": password,
                        "api_key": api_key,
                        "registered_at": time.time()
                    }

                    logger.info(f"Successfully registered account with API key: {sanitize_key_for_logging(api_key)}")
                    return account_info
                else:
                    raise Exception(f"Failed to extract API key, raw text: '{raw_text}'")
                    
            except Exception as e:
                logger.error(f"Registration failed: {e}")
                try:
                    # Take screenshot for debugging
                    screenshot_path = f"error_screenshot_{email.split('@')[0]}_{int(time.time())}.png"
                    await page.screenshot(path=screenshot_path)
                    logger.info(f"Error screenshot saved: {screenshot_path}")
                except:
                    pass

                try:
                    await browser.close()
                except:
                    pass

                return None
    
    async def register_multiple_keys(self, count: int) -> List[UpstreamKey]:
        """
        Register multiple new accounts and return UpstreamKey objects.
        
        Args:
            count: Number of keys to register
            
        Returns:
            List of new UpstreamKey objects
        """
        new_keys = []
        successful_registrations = 0
        
        logger.info(f"Starting registration of {count} new accounts...")
        
        for i in range(min(count, self.max_registration_attempts)):
            logger.info(f"Registration attempt {i + 1}/{count}")
            
            account_info = await self.register_single_account()
            
            if account_info:
                # Save account info
                self.save_account_info(account_info)
                
                # Create UpstreamKey object
                upstream_key = UpstreamKey(
                    key=account_info["api_key"],
                    weight=1,
                    enabled=True,
                    description=f"Auto-registered key for {account_info['first_name']} {account_info['last_name']}"
                )
                
                new_keys.append(upstream_key)
                successful_registrations += 1
                
                logger.info(f"Successfully registered key {i + 1}/{count}")
            else:
                logger.warning(f"Failed to register key {i + 1}/{count}")
            
            # Add delay between registrations with randomization
            if i < count - 1:
                if not account_info:
                    # Longer delay after failure
                    delay = random.randint(15, 25)
                    logger.info(f"Registration failed, waiting {delay} seconds before retry...")
                else:
                    # Shorter delay after success, but still randomized
                    delay = random.randint(8, 15)
                    logger.info(f"Registration successful, waiting {delay} seconds before next attempt...")

                await asyncio.sleep(delay)
        
        logger.info(f"Registration completed: {successful_registrations}/{count} successful")
        return new_keys
    
    def should_register_new_keys(self, active_keys_count: int) -> bool:
        """
        Check if new keys should be registered.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            True if new keys should be registered
        """
        return active_keys_count < self.min_keys
    
    def calculate_keys_needed(self, active_keys_count: int) -> int:
        """
        Calculate how many new keys are needed.
        
        Args:
            active_keys_count: Current number of active keys
            
        Returns:
            Number of keys to register
        """
        if active_keys_count >= self.min_keys:
            return 0
        
        return self.min_keys - active_keys_count
