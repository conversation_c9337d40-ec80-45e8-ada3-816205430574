"""
Auto registration functionality for API keys with CF5S bypass support.
"""

import asyncio
import json
import logging
import os
import random
import re
import string
import time
import urllib.parse
from typing import Dict, List, Optional

import requests
from faker import Faker
from playwright.async_api import async_playwright

from ..models import UpstreamKey
from ..utils import get_logger, sanitize_key_for_logging
from ..utils.cf_bypass import CloudflareBypass
from ..utils.cf5s_client import CF5SClient, CF5SPlaywrightIntegration

logger = get_logger(__name__)


class AutoRegistrar:
    """
    Automatic account registration and API key extraction.
    """

    def __init__(self, min_keys: int = 3, max_registration_attempts: int = 5, headless: bool = False):
        """
        Initialize the auto registrar.

        Args:
            min_keys: Minimum number of active keys to maintain
            max_registration_attempts: Maximum registration attempts per session
            headless: Whether to run browser in headless mode
        """
        self.min_keys = min_keys
        self.max_registration_attempts = max_registration_attempts
        self.headless = headless
        self.fake = Faker()
        self.accounts_file = "registered_accounts.json"
        self.base_url = "https://mail.hiclover.me"
        self.cf_bypass = CloudflareBypass()
        
        # CF5S configuration
        self.use_cf5s = os.getenv("USE_CF5S_BYPASS", "true").lower() == "true"
        self.cf5s_gateway_url = os.getenv("CF5S_GATEWAY_URL", "http://cf5s:8080")
        self.proxy_url = os.getenv("PROXY_URL", "socks5://warp:1080")
        
        logger.info(f"Auto registrar initialized with CF5S: {self.use_cf5s}")

    def generate_random_email(self) -> str:
        """Generate a random email address using hiclover.me service."""
        random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        return f"{random_string}@mail.hiclover.me"

    async def get_verification_code(self, email: str, max_attempts: int = 10) -> Optional[str]:
        """
        Get verification code from email.
        
        Args:
            email: Email address to check
            max_attempts: Maximum attempts to get code
            
        Returns:
            Verification code if found, None otherwise
        """
        username = email.split('@')[0]
        
        for attempt in range(max_attempts):
            try:
                # Get inbox
                response = requests.get(f"{self.base_url}/api/v1/mailbox/{username}")
                if response.status_code == 200:
                    data = response.json()
                    messages = data.get('messages', [])
                    
                    for message in messages:
                        subject = message.get('subject', '')
                        content = message.get('content', '')
                        
                        if 'verification' in subject.lower() or 'verify' in subject.lower():
                            # Extract verification code
                            code_match = re.search(r'\b(\d{6})\b', content)
                            if code_match:
                                code = code_match.group(1)
                                logger.info(f"Successfully extracted verification code: {code}")
                                return code
                
                # Wait before next attempt
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.warning(f"Error getting verification code (attempt {attempt + 1}): {e}")
                await asyncio.sleep(1)
        
        logger.error(f"Failed to get verification code after {max_attempts} attempts")
        return None

    async def save_account_info(self, account_info: Dict) -> None:
        """Save account information to file."""
        try:
            # Load existing accounts
            accounts = []
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r') as f:
                    accounts = json.load(f)
            
            # Add new account
            accounts.append(account_info)
            
            # Save updated accounts
            with open(self.accounts_file, 'w') as f:
                json.dump(accounts, f, indent=4)
            
            logger.info("Account information saved to registered_accounts.json")
            
        except Exception as e:
            logger.error(f"Failed to save account info: {e}")

    async def register_single_account(self) -> Optional[Dict]:
        """
        Register a single new account and extract API key.

        Returns:
            Account information if successful, None otherwise
        """
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        email = self.generate_random_email()
        password = ''.join(random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=16
        ))

        logger.info(f"Starting registration for: {first_name} {last_name}")

        # Choose bypass method based on configuration
        if self.use_cf5s:
            return await self._register_with_cf5s(first_name, last_name, email, password)
        else:
            return await self._register_with_playwright(first_name, last_name, email, password)

    async def _register_with_cf5s(self, first_name: str, last_name: str, email: str, password: str) -> Optional[Dict]:
        """Register account using CF5S bypass."""
        try:
            logger.info("Using CF5S bypass for registration")
            
            async with CF5SClient(self.cf5s_gateway_url, self.proxy_url) as cf5s_client:
                # Test CF5S connectivity first
                if not await cf5s_client.test_bypass():
                    logger.error("CF5S bypass test failed, falling back to Playwright")
                    return await self._register_with_playwright(first_name, last_name, email, password)
                
                # Use CF5S with Playwright
                async with async_playwright() as p:
                    browser = await self.cf_bypass.create_stealth_browser(p, headless=self.headless)
                    context = await self.cf_bypass.create_stealth_context(browser)
                    page = await context.new_page()
                    
                    # Setup CF5S integration
                    cf5s_integration = CF5SPlaywrightIntegration(cf5s_client)
                    
                    return await self._perform_registration_with_cf5s(
                        page, cf5s_integration, first_name, last_name, email, password, browser
                    )
                    
        except Exception as e:
            logger.error(f"CF5S registration failed: {e}, falling back to Playwright")
            return await self._register_with_playwright(first_name, last_name, email, password)

    async def _register_with_playwright(self, first_name: str, last_name: str, email: str, password: str) -> Optional[Dict]:
        """Register account using traditional Playwright bypass."""
        logger.info("Using traditional Playwright bypass for registration")
        
        async with async_playwright() as p:
            browser = await self.cf_bypass.create_stealth_browser(p, headless=self.headless)
            context = await self.cf_bypass.create_stealth_context(browser)
            page = await context.new_page()
            
            await self.cf_bypass.setup_stealth_page(page)
            
            return await self._perform_registration_traditional(
                page, first_name, last_name, email, password, browser
            )

    async def _perform_registration_with_cf5s(self, page, cf5s_integration, first_name: str, last_name: str, email: str, password: str, browser) -> Optional[Dict]:
        """Perform registration using CF5S bypass."""
        try:
            logger.info("Opening registration page with CF5S bypass...")

            # Use CF5S bypass navigation
            if not await cf5s_integration.navigate_with_bypass(page, "https://auth.ampcode.com/sign-up"):
                logger.error("CF5S navigation failed, trying traditional method")
                if not await self.cf_bypass.navigate_with_retry(page, "https://auth.ampcode.com/sign-up"):
                    raise Exception("Failed to navigate to registration page after multiple attempts")
            
            # Take screenshot for debugging
            await page.screenshot(path=f"debug_cf5s_page_load_{int(time.time())}.png")
            logger.info("Page loaded successfully with CF5S, took debug screenshot")

            # Continue with the same registration flow
            return await self._perform_form_filling(page, first_name, last_name, email, password, browser)
            
        except Exception as e:
            logger.error(f"CF5S registration failed: {e}")
            try:
                screenshot_path = f"error_cf5s_screenshot_{email.split('@')[0]}_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path)
                logger.info(f"Error screenshot saved: {screenshot_path}")
            except:
                pass
            finally:
                try:
                    await browser.close()
                except:
                    pass
            return None

    async def _perform_registration_traditional(self, page, first_name: str, last_name: str, email: str, password: str, browser) -> Optional[Dict]:
        """Perform registration using traditional Playwright bypass."""
        try:
            logger.info("Opening registration page with traditional bypass...")
            
            if not await self.cf_bypass.navigate_with_retry(page, "https://auth.ampcode.com/sign-up"):
                raise Exception("Failed to navigate to registration page after multiple attempts")
            
            await page.screenshot(path=f"debug_traditional_page_load_{int(time.time())}.png")
            logger.info("Page loaded successfully with traditional bypass, took debug screenshot")

            return await self._perform_form_filling(page, first_name, last_name, email, password, browser)
            
        except Exception as e:
            logger.error(f"Traditional registration failed: {e}")
            try:
                screenshot_path = f"error_traditional_screenshot_{email.split('@')[0]}_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path)
                logger.info(f"Error screenshot saved: {screenshot_path}")
            except:
                pass
            finally:
                try:
                    await browser.close()
                except:
                    pass
            return None

    async def _perform_form_filling(self, page, first_name: str, last_name: str, email: str, password: str, browser) -> Optional[Dict]:
        """Perform the actual form filling and registration process."""
        try:
            logger.info("Filling registration form...")

            # Try multiple selectors for first name input
            first_name_selectors = [
                'input[name="first_name"]',
                'input[name="firstName"]',
                'input[placeholder*="First"]',
                'input[id*="first"]',
                'input[class*="first"]'
            ]

            first_name_input = None
            for selector in first_name_selectors:
                try:
                    first_name_input = page.locator(selector)
                    await first_name_input.wait_for(timeout=15000)
                    logger.info(f"Found first name input with selector: {selector}")
                    break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            if not first_name_input:
                await page.screenshot(path=f"debug_no_input_{int(time.time())}.png")
                raise Exception("Could not find first name input field")

            # Use human-like typing
            await self.cf_bypass.human_like_typing(page, first_name_selectors[0], first_name)

            # Try multiple selectors for last name input
            last_name_selectors = [
                'input[name="last_name"]',
                'input[name="lastName"]',
                'input[placeholder*="Last"]',
                'input[id*="last"]',
                'input[class*="last"]'
            ]

            last_name_input = None
            for selector in last_name_selectors:
                try:
                    last_name_input = page.locator(selector)
                    await last_name_input.wait_for(timeout=15000)
                    logger.info(f"Found last name input with selector: {selector}")
                    break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            if not last_name_input:
                raise Exception("Could not find last name input field")

            await self.cf_bypass.human_like_typing(page, last_name_selectors[0], last_name)

            # Try multiple selectors for email input
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = page.locator(selector)
                    await email_input.wait_for(timeout=15000)
                    logger.info(f"Found email input with selector: {selector}")
                    break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            if not email_input:
                raise Exception("Could not find email input field")

            await self.cf_bypass.human_like_typing(page, email_selectors[0], email)

            # Submit form with human-like click
            await self.cf_bypass.human_like_click(page, 'button[type="submit"]')

            # Set password
            logger.info("Setting password...")
            password_input = page.locator('input[name="password"]')
            await password_input.wait_for(timeout=10000)

            await self.cf_bypass.human_like_typing(page, 'input[name="password"]', password)

            # Check registration result
            logger.info("Checking registration result...")
            await asyncio.sleep(3)

            # Check if we reached email verification page
            current_url = page.url
            if "verify" in current_url.lower() or "verification" in current_url.lower():
                logger.info("Successfully reached email verification page")
            else:
                # Take screenshot for debugging
                await page.screenshot(path=f"debug_after_submit_{int(time.time())}.png")
                page_content = await page.content()
                if "error" in page_content.lower() or "invalid" in page_content.lower():
                    raise Exception("Registration form submission failed")

            # Verify email
            logger.info("Verifying email...")
            verification_code = await self.get_verification_code(email)
            if not verification_code:
                raise Exception("Failed to get verification code")

            logger.info(f"Entering verification code: {verification_code}")
            code_input = page.locator('input[name="code"], input[placeholder*="code"], input[type="text"]')
            await code_input.wait_for(timeout=10000)
            await self.cf_bypass.human_like_typing(page, 'input[name="code"], input[placeholder*="code"], input[type="text"]', verification_code)

            # Submit verification
            verify_button = page.locator('button[type="submit"], button:has-text("Verify"), button:has-text("Continue")')
            await self.cf_bypass.human_like_click(page, 'button[type="submit"], button:has-text("Verify"), button:has-text("Continue")')

            # Wait for registration completion
            logger.info("Waiting for registration completion...")
            await asyncio.sleep(2)

            # Check if registration was successful
            current_url = page.url
            if "ampcode.com" in current_url and ("dashboard" in current_url or "settings" in current_url or current_url.endswith("ampcode.com/")):
                logger.info(f"Registration successful! Redirected to: {current_url}")
            else:
                await page.screenshot(path=f"debug_after_verify_{int(time.time())}.png")
                raise Exception(f"Registration verification failed, current URL: {current_url}")

            # Navigate to settings page to get API key
            logger.info("Navigating to settings page...")
            settings_url = "https://ampcode.com/settings"
            await page.goto(settings_url, timeout=30000)
            await asyncio.sleep(3)

            # Extract API key
            logger.info("Extracting API key...")
            api_key_element = page.locator('code, pre, .api-key, [data-key], input[readonly]')
            await api_key_element.wait_for(timeout=10000)

            raw_text = await api_key_element.text_content()
            if raw_text and raw_text.startswith('sgamp_user_'):
                api_key = raw_text.strip()

                # Save account information
                account_info = {
                    "first_name": first_name,
                    "last_name": last_name,
                    "email": email,
                    "password": password,
                    "api_key": api_key,
                    "registered_at": time.time()
                }

                await self.save_account_info(account_info)
                await browser.close()

                logger.info(f"Successfully registered account with API key: {sanitize_key_for_logging(api_key)}")
                return account_info
            else:
                raise Exception(f"Failed to extract API key, raw text: '{raw_text}'")

        except Exception as e:
            logger.error(f"Form filling failed: {e}")
            try:
                screenshot_path = f"error_form_screenshot_{email.split('@')[0]}_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path)
                logger.info(f"Error screenshot saved: {screenshot_path}")
            except:
                pass
            finally:
                try:
                    await browser.close()
                except:
                    pass
            return None

    async def register_multiple_keys(self, count: int) -> List[UpstreamKey]:
        """
        Register multiple new accounts and return API keys.

        Args:
            count: Number of accounts to register

        Returns:
            List of successfully registered UpstreamKey objects
        """
        logger.info(f"Starting registration of {count} new accounts...")
        registered_keys = []

        for i in range(min(count, self.max_registration_attempts)):
            try:
                logger.info(f"Registration attempt {i + 1}/{count}")

                account_info = await self.register_single_account()
                if account_info and account_info.get('api_key'):
                    # Create UpstreamKey object
                    key = UpstreamKey(
                        key=account_info['api_key'],
                        weight=1,
                        description=f"Auto-registered key for {account_info['first_name']} {account_info['last_name']}"
                    )
                    registered_keys.append(key)
                    logger.info(f"Successfully registered key {len(registered_keys)}/{count}")
                else:
                    logger.warning(f"Failed to register key {i + 1}/{count}")

                # Add delay between registrations
                if i < count - 1:
                    delay = random.uniform(10, 30)
                    logger.info(f"Waiting {delay:.1f}s before next registration...")
                    await asyncio.sleep(delay)

            except Exception as e:
                logger.error(f"Registration attempt {i + 1} failed: {e}")
                continue

        logger.info(f"Registration completed: {len(registered_keys)}/{count} successful")
        return registered_keys
