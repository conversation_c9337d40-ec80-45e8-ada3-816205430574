"""
Application settings and configuration management.

This module handles all application configuration including environment variables,
file paths, and runtime settings.
"""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import BaseSettings, Field, validator


class AppSettings(BaseSettings):
    """
    Main application settings.
    
    This class uses Pydantic's BaseSettings to automatically load configuration
    from environment variables, .env files, and provide sensible defaults.
    """
    
    # Application metadata
    app_name: str = Field("Anthropic API Proxy", description="Application name")
    app_version: str = Field("4.7.0", description="Application version")
    app_description: str = Field(
        "Advanced Anthropic API Proxy with Key Management",
        description="Application description"
    )
    
    # Server configuration
    host: str = Field("0.0.0.0", description="Server host")
    port: int = Field(8000, ge=1, le=65535, description="Server port")
    debug: bool = Field(False, description="Debug mode")
    reload: bool = Field(False, description="Auto-reload on code changes")
    
    # API configuration
    target_api_url: str = Field(
        "https://ampcode.com/api/provider/anthropic/v1/messages",
        description="Target Anthropic API URL"
    )
    anthropic_version: str = Field("2023-06-01", description="Anthropic API version")
    
    # File paths
    keys_config_file: str = Field("keys.json", description="Path to keys configuration file")
    models_config_file: str = Field("models.json", description="Path to models configuration file")
    log_file: Optional[str] = Field(None, description="Path to log file (None for stdout)")
    
    # Timeout settings
    request_timeout: float = Field(120.0, ge=1.0, description="Request timeout in seconds")
    connect_timeout: float = Field(10.0, ge=1.0, description="Connection timeout in seconds")
    
    # Retry settings
    max_retries: int = Field(3, ge=0, le=10, description="Maximum number of retries")
    retry_delay: float = Field(1.0, ge=0.1, description="Delay between retries in seconds")
    
    # Health check settings
    health_check_interval: int = Field(
        300, ge=60, description="Health check interval in seconds"
    )
    failure_threshold: int = Field(
        3, ge=1, le=10, description="Number of failures before disabling a key"
    )
    recovery_time: int = Field(
        300, ge=60, description="Time to wait before attempting key recovery in seconds"
    )
    
    # Load balancer settings
    default_load_balancer_strategy: str = Field(
        "round_robin", description="Default load balancing strategy"
    )
    
    # Logging settings
    log_level: str = Field("INFO", description="Logging level")
    log_format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    
    # Security settings
    allowed_origins: List[str] = Field(
        default_factory=lambda: ["*"], description="CORS allowed origins"
    )
    allowed_methods: List[str] = Field(
        default_factory=lambda: ["*"], description="CORS allowed methods"
    )
    allowed_headers: List[str] = Field(
        default_factory=lambda: ["*"], description="CORS allowed headers"
    )
    
    # Rate limiting (future feature)
    enable_rate_limiting: bool = Field(False, description="Enable rate limiting")
    default_rate_limit: int = Field(100, ge=1, description="Default rate limit per minute")
    
    # Monitoring and metrics
    enable_metrics: bool = Field(True, description="Enable metrics collection")
    metrics_endpoint: str = Field("/metrics", description="Metrics endpoint path")
    
    class Config:
        """Pydantic configuration."""
        
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        v_upper = v.upper()
        if v_upper not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v_upper
    
    @validator('default_load_balancer_strategy')
    def validate_load_balancer_strategy(cls, v):
        """Validate load balancer strategy."""
        valid_strategies = {'round_robin', 'weighted_round_robin', 'random', 'least_used'}
        if v not in valid_strategies:
            raise ValueError(f"Load balancer strategy must be one of {valid_strategies}")
        return v
    
    @property
    def base_dir(self) -> Path:
        """Get the base directory of the application."""
        return Path(__file__).parent.parent.parent
    
    @property
    def keys_config_path(self) -> Path:
        """Get the full path to the keys configuration file."""
        if os.path.isabs(self.keys_config_file):
            return Path(self.keys_config_file)
        return self.base_dir / self.keys_config_file
    
    @property
    def models_config_path(self) -> Path:
        """Get the full path to the models configuration file."""
        if os.path.isabs(self.models_config_file):
            return Path(self.models_config_file)
        return self.base_dir / self.models_config_file
    
    @property
    def log_file_path(self) -> Optional[Path]:
        """Get the full path to the log file."""
        if not self.log_file:
            return None
        if os.path.isabs(self.log_file):
            return Path(self.log_file)
        return self.base_dir / self.log_file


class DatabaseSettings(BaseSettings):
    """
    Database settings (for future use).
    
    Currently the application uses JSON files for configuration,
    but this class is prepared for future database integration.
    """
    
    database_url: Optional[str] = Field(None, description="Database connection URL")
    database_echo: bool = Field(False, description="Echo SQL queries")
    database_pool_size: int = Field(5, ge=1, description="Database connection pool size")
    database_max_overflow: int = Field(10, ge=0, description="Database max overflow connections")
    
    class Config:
        """Pydantic configuration."""
        
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = AppSettings()
database_settings = DatabaseSettings()
