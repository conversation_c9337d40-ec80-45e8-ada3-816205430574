#!/bin/bash

# AMP Docker Deployment Script
# This script helps deploy the Anthropic API Proxy using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p data logs
    print_success "Directories created"
}

# Function to check configuration files
check_config() {
    print_status "Checking configuration files..."
    
    if [ ! -f "data/keys.json" ]; then
        if [ -f "keys.json" ]; then
            cp keys.json data/
            print_success "Copied keys.json to data directory"
        else
            print_error "keys.json not found. Please ensure you have a valid keys.json file."
            exit 1
        fi
    fi
    
    if [ ! -f "data/models.json" ]; then
        if [ -f "models.json" ]; then
            cp models.json data/
            print_success "Copied models.json to data directory"
        else
            print_error "models.json not found. Please ensure you have a valid models.json file."
            exit 1
        fi
    fi
    
    print_success "Configuration files are ready"
}

# Function to build and start the application
start_application() {
    print_status "Building and starting AMP Proxy..."
    
    # Build the image
    docker-compose build
    
    # Start the services
    docker-compose up -d
    
    print_success "AMP Proxy is starting..."
    print_status "Waiting for application to be ready..."
    
    # Wait for the application to be ready
    for i in {1..30}; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            print_success "AMP Proxy is ready and healthy!"
            break
        fi
        if [ $i -eq 30 ]; then
            print_warning "Application may still be starting. Check logs with: docker-compose logs -f"
        fi
        sleep 2
    done
}

# Function to show status
show_status() {
    print_status "Application Status:"
    docker-compose ps
    
    echo ""
    print_status "Application URLs:"
    echo "  Health Check: http://localhost:8000/health"
    echo "  API Endpoint: http://localhost:8000/v1/messages"
    echo "  Admin Stats:  http://localhost:8000/admin/stats"
    
    echo ""
    print_status "Useful Commands:"
    echo "  View logs:    docker-compose logs -f"
    echo "  Stop app:     docker-compose down"
    echo "  Restart app:  docker-compose restart"
    echo "  Update app:   docker-compose pull && docker-compose up -d"
}

# Main execution
main() {
    echo "=========================================="
    echo "  AMP Proxy Docker Deployment"
    echo "=========================================="
    echo ""
    
    check_docker
    check_docker_compose
    create_directories
    check_config
    start_application
    
    echo ""
    echo "=========================================="
    show_status
    echo "=========================================="
    echo ""
    print_success "Deployment completed successfully!"
}

# Handle command line arguments
case "${1:-start}" in
    start)
        main
        ;;
    stop)
        print_status "Stopping AMP Proxy..."
        docker-compose down
        print_success "AMP Proxy stopped"
        ;;
    restart)
        print_status "Restarting AMP Proxy..."
        docker-compose restart
        print_success "AMP Proxy restarted"
        ;;
    logs)
        docker-compose logs -f
        ;;
    status)
        show_status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status}"
        echo ""
        echo "Commands:"
        echo "  start   - Build and start the application (default)"
        echo "  stop    - Stop the application"
        echo "  restart - Restart the application"
        echo "  logs    - Show application logs"
        echo "  status  - Show application status"
        exit 1
        ;;
esac
