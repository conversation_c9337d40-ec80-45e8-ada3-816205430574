@echo off
setlocal enabledelayedexpansion

REM AMP Docker Deployment Script for Windows
REM This script helps deploy the Anthropic API Proxy using Docker Compose

title AMP Proxy Docker Deployment

echo ==========================================
echo   AMP Proxy Docker Deployment
echo ==========================================
echo.

REM Check if Docker is running
echo [INFO] Checking Docker status...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)
echo [SUCCESS] Docker is running

REM Check if docker-compose is available
echo [INFO] Checking docker-compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] docker-compose is not available. Please install Docker Desktop with docker-compose.
    pause
    exit /b 1
)
echo [SUCCESS] docker-compose is available

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo [SUCCESS] Directories created

REM Check configuration files
echo [INFO] Checking configuration files...
if not exist "data\keys.json" (
    if exist "keys.json" (
        copy "keys.json" "data\" >nul
        echo [SUCCESS] Copied keys.json to data directory
    ) else (
        echo [ERROR] keys.json not found. Please ensure you have a valid keys.json file.
        pause
        exit /b 1
    )
)

if not exist "data\models.json" (
    if exist "models.json" (
        copy "models.json" "data\" >nul
        echo [SUCCESS] Copied models.json to data directory
    ) else (
        echo [ERROR] models.json not found. Please ensure you have a valid models.json file.
        pause
        exit /b 1
    )
)
echo [SUCCESS] Configuration files are ready

REM Handle command line arguments
set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="start" goto :start
if "%command%"=="stop" goto :stop
if "%command%"=="restart" goto :restart
if "%command%"=="logs" goto :logs
if "%command%"=="status" goto :status
goto :usage

:start
echo [INFO] Building and starting AMP Proxy...
echo.

REM Build the image
echo [INFO] Building Docker image...
docker-compose build
if errorlevel 1 (
    echo [ERROR] Failed to build Docker image
    pause
    exit /b 1
)

REM Start the services
echo [INFO] Starting services...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)

echo [SUCCESS] AMP Proxy is starting...
echo [INFO] Waiting for application to be ready...

REM Wait for the application to be ready
set /a "attempts=0"
:wait_loop
set /a "attempts+=1"
if !attempts! gtr 30 goto :wait_timeout

timeout /t 2 /nobreak >nul
curl -f http://localhost:8000/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] AMP Proxy is ready and healthy!
    goto :show_status
)
goto :wait_loop

:wait_timeout
echo [WARNING] Application may still be starting. Check logs with: docker-compose logs -f

:show_status
echo.
echo ==========================================
echo [INFO] Application Status:
docker-compose ps
echo.
echo [INFO] Application URLs:
echo   Health Check: http://localhost:8000/health
echo   API Endpoint: http://localhost:8000/v1/messages
echo   Admin Stats:  http://localhost:8000/admin/stats
echo.
echo [INFO] Useful Commands:
echo   View logs:    docker-compose logs -f
echo   Stop app:     docker-compose down
echo   Restart app:  docker-compose restart
echo ==========================================
echo.
echo [SUCCESS] Deployment completed successfully!
pause
exit /b 0

:stop
echo [INFO] Stopping AMP Proxy...
docker-compose down
echo [SUCCESS] AMP Proxy stopped
pause
exit /b 0

:restart
echo [INFO] Restarting AMP Proxy...
docker-compose restart
echo [SUCCESS] AMP Proxy restarted
pause
exit /b 0

:logs
docker-compose logs -f
exit /b 0

:status
echo [INFO] Application Status:
docker-compose ps
echo.
echo [INFO] Application URLs:
echo   Health Check: http://localhost:8000/health
echo   API Endpoint: http://localhost:8000/v1/messages
echo   Admin Stats:  http://localhost:8000/admin/stats
pause
exit /b 0

:usage
echo Usage: %0 {start^|stop^|restart^|logs^|status}
echo.
echo Commands:
echo   start   - Build and start the application (default)
echo   stop    - Stop the application
echo   restart - Restart the application
echo   logs    - Show application logs
echo   status  - Show application status
pause
exit /b 1
