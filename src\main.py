"""
Main application module for the Anthropic API Proxy.

This module contains the FastAPI application setup, lifecycle management,
and router registration.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict

import httpx
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config import settings
from .key_manager import key_manager
from .models import ErrorResponse
from .routers import admin, core, health
from .routers.core import set_http_client
from .utils import get_logger, log_error_with_context, setup_logging

# Setup logging first
setup_logging()
logger = get_logger(__name__)

# Global HTTP client
http_client: httpx.AsyncClient = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    This function handles the startup and shutdown of the application,
    including initialization of global resources and cleanup.
    
    Args:
        app: FastAPI application instance
    """
    # Startup
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    
    try:
        # Initialize HTTP client
        global http_client
        timeout = httpx.Timeout(
            timeout=settings.request_timeout,
            connect=settings.connect_timeout
        )
        http_client = httpx.AsyncClient(timeout=timeout)
        set_http_client(http_client)
        logger.info("HTTP client initialized")
        
        # Start key manager
        await key_manager.start()
        logger.info("Key manager started")
        
        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Starting application shutdown")
    
    try:
        # Stop key manager
        await key_manager.stop()
        logger.info("Key manager stopped")
        
        # Close HTTP client
        if http_client:
            await http_client.aclose()
            logger.info("HTTP client closed")
        
        logger.info("Application shutdown completed successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application instance
    """
    # Create FastAPI app
    app = FastAPI(
        title=settings.app_name,
        description=settings.app_description,
        version=settings.app_version,
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=settings.allowed_methods,
        allow_headers=settings.allowed_headers,
    )
    
    # Register routers
    app.include_router(health.router)
    app.include_router(core.router)
    app.include_router(admin.router)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    logger.info("FastAPI application created and configured")
    return app


def setup_exception_handlers(app: FastAPI) -> None:
    """
    Set up global exception handlers for the application.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """Handle HTTP exceptions."""
        logger.warning(
            f"HTTP exception {exc.status_code}: {exc.detail} "
            f"for {request.method} {request.url.path}"
        )
        
        error_response = ErrorResponse(
            error=exc.detail,
            code=str(exc.status_code),
            type="HTTPException"
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response.dict()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """Handle general exceptions."""
        log_error_with_context(
            logger, exc,
            f"Unhandled exception for {request.method} {request.url.path}"
        )
        
        error_response = ErrorResponse.from_exception(
            exc,
            detail="An internal server error occurred"
        )
        
        return JSONResponse(
            status_code=500,
            content=error_response.dict()
        )
    
    @app.exception_handler(asyncio.TimeoutError)
    async def timeout_exception_handler(request: Request, exc: asyncio.TimeoutError) -> JSONResponse:
        """Handle timeout exceptions."""
        logger.warning(
            f"Request timeout for {request.method} {request.url.path}"
        )
        
        error_response = ErrorResponse(
            error="Request timeout",
            detail="The request took too long to process",
            type="TimeoutError"
        )
        
        return JSONResponse(
            status_code=504,
            content=error_response.dict()
        )


# Create the application instance
app = create_app()


def main() -> None:
    """
    Main entry point for running the application.
    
    This function is used when running the application directly
    or through the console script defined in pyproject.toml.
    """
    logger.info(f"Starting server on {settings.host}:{settings.port}")
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
        access_log=True,
        server_header=False,
        date_header=False,
    )


if __name__ == "__main__":
    main()
