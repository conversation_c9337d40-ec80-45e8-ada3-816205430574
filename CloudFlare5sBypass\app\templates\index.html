<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudFlare 5s</title>
    <!-- 引入 Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>CloudFlare 5s</h4>
                </div>
                <div class="card-body">
                    <form id="addressForm">
                        <div class="form-group">
                            <label for="url">Cloudflare 地址</label>
                            <input type="text" class="form-control" id="url" name="url" required>
                        </div>
                        <div class="form-group">
                            <label for="proxy_server">代理地址</label>
                            <input type="text" class="form-control" id="proxy_server" name="proxy_server" required>
                        </div>
                        <div class="form-group">
                            <label for="proxy_server">userAgent</label>
                            <input type="text" class="form-control" id="user_agent" name="user_agent" required>
                        </div>
                        <button type="submit" class="btn btn-primary">提交</button>
                    </form>
                    <div id="response" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入 jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- 引入 Bootstrap JS -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script>
    $(document).ready(function () {
        $('#user_agent').val(navigator.userAgent);

        $('#addressForm').on('submit', function (event) {
            event.preventDefault();

            var formData = {
                url: $('#url').val(),
                proxy_server: $('#proxy_server').val(),
                user_agent: $('#user_agent').val()
            };
            $('#response').html("正在查询中，请稍候...");
            $.ajax({
                type: 'POST',
                url: '/cloudflare5s/bypass-v2', // 替换为实际的服务器端处理 URL
                data: JSON.stringify(formData),
                contentType: 'application/json',
                success: function (response) {
                    $('#response').html('<div class="alert alert-success">提交成功: ' + JSON.stringify(response) + '</div>');
                },
                error: function (error) {
                    $('#response').html('<div class="alert alert-danger">提交失败: ' + JSON.stringify(error.responseJSON) + '</div>');
                }
            });
        });
    });
</script>
</body>
</html>
