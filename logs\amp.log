2025-06-26 08:27:49,190 - root - INFO - Logging to file: /app/logs/amp.log
2025-06-26 08:27:49,191 - root - INFO - Logging configured with level: INFO
2025-06-26 08:27:49,201 - src.main - INFO - FastAPI application created and configured
2025-06-26 08:27:49,209 - src.main - INFO - Starting server on 0.0.0.0:8000
2025-06-26 08:27:49,233 - src.main - INFO - Starting Anthropic API Proxy v4.7.0
2025-06-26 08:27:49,279 - src.main - INFO - HTTP client initialized
2025-06-26 08:27:49,280 - src.key_manager.manager - INFO - Key manager started with health monitoring
2025-06-26 08:27:49,281 - src.main - INFO - Key manager started
2025-06-26 08:27:49,282 - src.main - INFO - Application startup completed successfully
2025-06-26 08:27:49,283 - src.key_manager.health - INFO - Starting health monitoring with 300s interval
2025-06-26 08:27:49,285 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:28:05,161 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:28:05,162 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:28:05,163 - src.routers.core - INFO - Request started: GET /v1/models client=KEY(...over)
2025-06-26 08:28:05,168 - src.config.loader - INFO - Successfully loaded configuration from /app/data/models.json
2025-06-26 08:28:05,169 - src.config.loader - INFO - Successfully loaded 4 models from /app/data/models.json
2025-06-26 08:28:05,170 - src.routers.core - INFO - Request completed: GET /v1/models status=200 time=0.007s
2025-06-26 08:29:21,161 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:29:21,163 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:29:21,164 - src.routers.core - INFO - Request started: GET /v1/models client=KEY(...over)
2025-06-26 08:29:21,170 - src.config.loader - INFO - Successfully loaded configuration from /app/data/models.json
2025-06-26 08:29:21,171 - src.config.loader - INFO - Successfully loaded 4 models from /app/data/models.json
2025-06-26 08:29:21,173 - src.routers.core - INFO - Request completed: GET /v1/models status=200 time=0.008s
2025-06-26 08:29:34,749 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:29:34,750 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:32:37,875 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:37:26,506 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:37:46,525 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:37:46,526 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:38:21,267 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:38:21,268 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:39:58,979 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:39:58,980 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:41:26,033 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:41:26,034 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:41:26,035 - src.routers.admin - INFO - Manual registration requested for 1 keys
2025-06-26 08:41:26,036 - src.key_manager.auto_registrar - INFO - Starting registration of 1 new accounts...
2025-06-26 08:41:26,037 - src.key_manager.auto_registrar - INFO - Registration attempt 1/1
2025-06-26 08:41:26,040 - src.key_manager.auto_registrar - INFO - Generated temporary email: KEY(...r.me)
2025-06-26 08:41:26,041 - src.key_manager.auto_registrar - INFO - Starting registration for: Caitlin Ramirez
2025-06-26 08:41:27,388 - src.key_manager.auto_registrar - INFO - Opening registration page...
2025-06-26 08:41:34,631 - src.key_manager.auto_registrar - INFO - Filling registration form...
2025-06-26 08:41:38,373 - src.key_manager.auto_registrar - INFO - Setting password...
2025-06-26 08:41:41,483 - src.key_manager.auto_registrar - INFO - Checking registration result...
2025-06-26 08:41:44,495 - src.key_manager.auto_registrar - INFO - Successfully reached email verification page
2025-06-26 08:41:44,497 - src.key_manager.auto_registrar - INFO - Verifying email...
2025-06-26 08:41:44,498 - src.key_manager.auto_registrar - INFO - Checking mailbox KEY(...r.me) for verification code...
2025-06-26 08:41:45,855 - src.key_manager.auto_registrar - INFO - Successfully extracted verification code: 304669
2025-06-26 08:41:45,857 - src.key_manager.auto_registrar - INFO - Entering verification code: 304669
2025-06-26 08:41:48,744 - src.key_manager.auto_registrar - INFO - Waiting for registration completion...
2025-06-26 08:41:50,747 - src.key_manager.auto_registrar - INFO - Registration successful! Redirected to: https://ampcode.com/
2025-06-26 08:41:50,748 - src.key_manager.auto_registrar - INFO - Navigating to settings page...
2025-06-26 08:41:57,461 - src.key_manager.auto_registrar - INFO - Successfully loaded settings page: https://ampcode.com/settings
2025-06-26 08:41:57,593 - src.key_manager.auto_registrar - INFO - Successfully registered account with API key: KEY(...0ea1)
2025-06-26 08:41:57,606 - src.key_manager.auto_registrar - INFO - Account information saved to registered_accounts.json
2025-06-26 08:41:57,607 - src.key_manager.auto_registrar - INFO - Successfully registered key 1/1
2025-06-26 08:41:57,608 - src.key_manager.auto_registrar - INFO - Registration completed: 1/1 successful
2025-06-26 08:41:57,614 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:41:57,615 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:42:15,115 - src.key_manager.health - INFO - Health check completed: 6/6 keys checked, 0 recovered, 0 registered, 6 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:42:41,605 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:42:41,606 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:20,726 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:43:20,727 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:20,732 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:43:20,733 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:20,734 - src.key_manager.manager - INFO - Key event: disabled KEY(...402c) - Disabled manually
2025-06-26 08:43:32,273 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:43:32,274 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:43,825 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:43:43,826 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:43,829 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:43:43,830 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:43:43,831 - src.key_manager.manager - INFO - Key event: disabled KEY(...af74) - Disabled manually
2025-06-26 08:44:26,698 - src.main - INFO - Starting application shutdown
2025-06-26 08:44:26,699 - src.key_manager.health - INFO - Health monitoring stopped
2025-06-26 08:44:26,700 - src.key_manager.manager - INFO - Key manager stopped
2025-06-26 08:44:26,701 - src.main - INFO - Key manager stopped
2025-06-26 08:44:26,708 - src.main - INFO - HTTP client closed
2025-06-26 08:44:26,709 - src.main - INFO - Application shutdown completed successfully
2025-06-26 08:44:29,194 - root - INFO - Logging to file: /app/logs/amp.log
2025-06-26 08:44:29,195 - root - INFO - Logging configured with level: INFO
2025-06-26 08:44:29,206 - src.main - INFO - FastAPI application created and configured
2025-06-26 08:44:29,213 - src.main - INFO - Starting server on 0.0.0.0:8000
2025-06-26 08:44:29,254 - src.main - INFO - Starting Anthropic API Proxy v4.7.0
2025-06-26 08:44:29,305 - src.main - INFO - HTTP client initialized
2025-06-26 08:44:29,306 - src.key_manager.manager - INFO - Key manager started with health monitoring
2025-06-26 08:44:29,307 - src.main - INFO - Key manager started
2025-06-26 08:44:29,308 - src.main - INFO - Application startup completed successfully
2025-06-26 08:44:29,309 - src.key_manager.health - INFO - Starting health monitoring with 300s interval
2025-06-26 08:44:29,310 - src.key_manager.health - WARNING - Active keys (4) below minimum (5). Attempting to register 1 new keys...
2025-06-26 08:44:29,310 - src.key_manager.auto_registrar - INFO - Starting registration of 1 new accounts...
2025-06-26 08:44:29,311 - src.key_manager.auto_registrar - INFO - Registration attempt 1/1
2025-06-26 08:44:29,312 - src.key_manager.auto_registrar - INFO - Generated temporary email: KEY(...r.me)
2025-06-26 08:44:29,314 - src.key_manager.auto_registrar - INFO - Starting registration for: Craig Fernandez
2025-06-26 08:44:30,279 - src.key_manager.auto_registrar - INFO - Opening registration page...
2025-06-26 08:44:35,731 - src.key_manager.auto_registrar - INFO - Filling registration form...
2025-06-26 08:44:40,571 - src.key_manager.auto_registrar - INFO - Setting password...
2025-06-26 08:44:43,669 - src.key_manager.auto_registrar - INFO - Checking registration result...
2025-06-26 08:44:46,678 - src.key_manager.auto_registrar - INFO - Successfully reached email verification page
2025-06-26 08:44:46,679 - src.key_manager.auto_registrar - INFO - Verifying email...
2025-06-26 08:44:46,680 - src.key_manager.auto_registrar - INFO - Checking mailbox KEY(...r.me) for verification code...
2025-06-26 08:44:47,456 - src.key_manager.auto_registrar - INFO - Successfully extracted verification code: 500157
2025-06-26 08:44:47,457 - src.key_manager.auto_registrar - INFO - Entering verification code: 500157
2025-06-26 08:44:50,327 - src.key_manager.auto_registrar - INFO - Waiting for registration completion...
2025-06-26 08:44:52,331 - src.key_manager.auto_registrar - INFO - Registration successful! Redirected to: https://ampcode.com/
2025-06-26 08:44:52,333 - src.key_manager.auto_registrar - INFO - Navigating to settings page...
2025-06-26 08:44:57,169 - src.key_manager.auto_registrar - INFO - Successfully loaded settings page: https://ampcode.com/settings
2025-06-26 08:44:57,289 - src.key_manager.auto_registrar - INFO - Successfully registered account with API key: KEY(...c71d)
2025-06-26 08:44:57,298 - src.key_manager.auto_registrar - INFO - Account information saved to registered_accounts.json
2025-06-26 08:44:57,300 - src.key_manager.auto_registrar - INFO - Successfully registered key 1/1
2025-06-26 08:44:57,301 - src.key_manager.auto_registrar - INFO - Registration completed: 1/1 successful
2025-06-26 08:44:57,305 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:44:57,306 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:44:57,307 - src.key_manager.health - INFO - Successfully registered 1 new keys
2025-06-26 08:44:57,308 - src.key_manager.health - INFO - Health check completed: 6/6 keys checked, 0 recovered, 1 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 08:46:24,637 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:46:24,640 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:49:45,825 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 08:54:34,342 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 08:59:22,934 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 09:04:11,350 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 09:08:59,763 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
2025-06-26 09:13:48,209 - src.key_manager.health - INFO - Health check completed: 7/7 keys checked, 0 recovered, 0 registered, 5 active, 2 temp disabled, 0 perm disabled
