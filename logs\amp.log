2025-06-26 08:27:49,190 - root - INFO - Logging to file: /app/logs/amp.log
2025-06-26 08:27:49,191 - root - INFO - Logging configured with level: INFO
2025-06-26 08:27:49,201 - src.main - INFO - FastAPI application created and configured
2025-06-26 08:27:49,209 - src.main - INFO - Starting server on 0.0.0.0:8000
2025-06-26 08:27:49,233 - src.main - INFO - Starting Anthropic API Proxy v4.7.0
2025-06-26 08:27:49,279 - src.main - INFO - HTTP client initialized
2025-06-26 08:27:49,280 - src.key_manager.manager - INFO - Key manager started with health monitoring
2025-06-26 08:27:49,281 - src.main - INFO - Key manager started
2025-06-26 08:27:49,282 - src.main - INFO - Application startup completed successfully
2025-06-26 08:27:49,283 - src.key_manager.health - INFO - Starting health monitoring with 300s interval
2025-06-26 08:27:49,285 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:28:05,161 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:28:05,162 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:28:05,163 - src.routers.core - INFO - Request started: GET /v1/models client=KEY(...over)
2025-06-26 08:28:05,168 - src.config.loader - INFO - Successfully loaded configuration from /app/data/models.json
2025-06-26 08:28:05,169 - src.config.loader - INFO - Successfully loaded 4 models from /app/data/models.json
2025-06-26 08:28:05,170 - src.routers.core - INFO - Request completed: GET /v1/models status=200 time=0.007s
2025-06-26 08:29:21,161 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:29:21,163 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:29:21,164 - src.routers.core - INFO - Request started: GET /v1/models client=KEY(...over)
2025-06-26 08:29:21,170 - src.config.loader - INFO - Successfully loaded configuration from /app/data/models.json
2025-06-26 08:29:21,171 - src.config.loader - INFO - Successfully loaded 4 models from /app/data/models.json
2025-06-26 08:29:21,173 - src.routers.core - INFO - Request completed: GET /v1/models status=200 time=0.008s
2025-06-26 08:29:34,749 - src.config.loader - INFO - Successfully saved configuration to /app/data/keys.json
2025-06-26 08:29:34,750 - src.config.loader - INFO - Successfully saved KeysConfig to /app/data/keys.json
2025-06-26 08:32:37,875 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
2025-06-26 08:37:26,506 - src.key_manager.health - INFO - Health check completed: 5/5 keys checked, 0 recovered, 0 registered, 5 active, 0 temp disabled, 0 perm disabled
