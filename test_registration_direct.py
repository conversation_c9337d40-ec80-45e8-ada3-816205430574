#!/usr/bin/env python3
"""
Direct test for auto registration functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.key_manager.auto_registrar import AutoRegistrar
from src.utils import setup_logging, get_logger

logger = get_logger(__name__)


async def main():
    """Test registration directly."""
    print("🧪 Direct Registration Test")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Initialize registrar with visible browser
    registrar = AutoRegistrar(
        min_keys=1,
        max_registration_attempts=1,
        headless=False  # Keep browser visible for debugging
    )
    
    print("ℹ️  Starting direct registration test...")
    print("ℹ️  <PERSON><PERSON><PERSON> will open and attempt registration")
    print("ℹ️  This may take 2-3 minutes...")
    print("ℹ️  Watch the browser window for progress")
    
    try:
        # Test single registration
        account_info = await registrar.register_single_account()
        
        if account_info:
            print("\n🎉 REGISTRATION SUCCESSFUL!")
            print("=" * 50)
            print(f"Name: {account_info['first_name']} {account_info['last_name']}")
            print(f"Email: {account_info['email']}")
            print(f"Password: {account_info['password']}")
            print(f"API Key: {account_info['api_key']}")
            print(f"Registered at: {account_info['registered_at']}")
            
            # Save the account info
            registrar.save_account_info(account_info)
            print(f"\n✅ Account saved to: {registrar.accounts_file}")
            
            print("\n🔧 Integration Ready!")
            print("The auto registration functionality is working correctly.")
            print("You can now use it in the main system.")
            
        else:
            print("\n❌ REGISTRATION FAILED!")
            print("=" * 50)
            print("The registration did not complete successfully.")
            print("Check the browser window and error screenshots for details.")
            
    except Exception as e:
        print(f"\n💥 REGISTRATION ERROR!")
        print("=" * 50)
        print(f"Error: {e}")
        print("Check the browser window and error screenshots for details.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user.")
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
        sys.exit(1)
