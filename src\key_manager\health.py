"""
Health monitoring for API keys.

This module handles the health monitoring and automatic recovery
of upstream API keys.
"""

import asyncio
import logging
from typing import TYPE_CHECKING

from ..config import settings
from ..utils import get_current_datetime, get_logger, is_expired, log_key_event

if TYPE_CHECKING:
    from .manager import KeyManager

logger = get_logger(__name__)


class HealthMonitor:
    """
    Health monitor for upstream API keys.
    
    This class is responsible for:
    - Periodically checking the health of disabled keys
    - Attempting to recover temporarily disabled keys
    - Logging health check results
    """
    
    def __init__(self, key_manager: "KeyManager"):
        """
        Initialize the health monitor.
        
        Args:
            key_manager: Reference to the key manager
        """
        self.key_manager = key_manager
        self._running = False
    
    async def start_monitoring(self) -> None:
        """Start the health monitoring loop."""
        self._running = True
        logger.info(
            f"Starting health monitoring with {settings.health_check_interval}s interval"
        )
        
        while self._running:
            try:
                await self.perform_health_check()
                await asyncio.sleep(settings.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
        
        logger.info("Health monitoring stopped")
    
    def stop_monitoring(self) -> None:
        """Stop the health monitoring loop."""
        self._running = False
    
    async def perform_health_check(self) -> None:
        """Perform a health check on all upstream keys."""
        config = self.key_manager.config
        
        total_keys = len(config.upstream_keys)
        recovered_keys = 0
        checked_keys = 0
        
        logger.debug("Starting health check")
        
        for key in config.upstream_keys:
            if await self.check_key_health(key):
                recovered_keys += 1
            checked_keys += 1
        
        # Log summary
        stats = self.key_manager.get_statistics()
        logger.info(
            f"Health check completed: {checked_keys}/{total_keys} keys checked, "
            f"{recovered_keys} recovered, {stats.active_upstream_keys} active, "
            f"{stats.temporarily_disabled_keys} temp disabled, "
            f"{stats.permanently_disabled_keys} perm disabled"
        )
    
    async def check_key_health(self, key) -> bool:
        """
        Check the health of a single key and attempt recovery if needed.
        
        Args:
            key: UpstreamKey to check
            
        Returns:
            True if key was recovered, False otherwise
        """
        # Skip permanently disabled keys
        if key.permanently_disabled:
            return False
        
        # Skip enabled keys
        if key.enabled:
            return False
        
        # Skip keys that haven't failed
        if not key.last_failed_time:
            return False
        
        # Check if enough time has passed for recovery
        if not is_expired(key.last_failed_time, settings.recovery_time):
            return False
        
        # Attempt recovery
        return await self.attempt_key_recovery(key)
    
    async def attempt_key_recovery(self, key) -> bool:
        """
        Attempt to recover a failed key.
        
        Args:
            key: UpstreamKey to recover
            
        Returns:
            True if recovery was attempted, False otherwise
        """
        if key.recovery_attempted:
            # Already attempted recovery, don't try again
            return False
        
        # Mark recovery as attempted
        key.recovery_attempted = True
        key.enabled = True
        key.reset_failures()
        
        # Save the configuration
        self.key_manager.save_config()
        
        log_key_event(
            logger, "recovery_attempted", key.key,
            "Attempting recovery after failure threshold"
        )
        
        return True
    
    def get_health_summary(self) -> dict:
        """
        Get a summary of the current health status.
        
        Returns:
            Dictionary with health summary information
        """
        stats = self.key_manager.get_statistics()
        config = self.key_manager.config
        
        # Calculate additional metrics
        recovery_pending = 0
        recovery_attempted = 0
        
        for key in config.upstream_keys:
            if not key.enabled and not key.permanently_disabled and not key.recovery_attempted:
                recovery_pending += 1
            elif key.recovery_attempted:
                recovery_attempted += 1
        
        return {
            "total_keys": stats.total_upstream_keys,
            "active_keys": stats.active_upstream_keys,
            "temporarily_disabled": stats.temporarily_disabled_keys,
            "permanently_disabled": stats.permanently_disabled_keys,
            "recovery_pending": recovery_pending,
            "recovery_attempted": recovery_attempted,
            "monitoring_active": self._running,
            "check_interval": settings.health_check_interval,
            "recovery_time": settings.recovery_time,
            "failure_threshold": settings.failure_threshold,
        }
    
    def get_key_health_details(self) -> list:
        """
        Get detailed health information for all keys.
        
        Returns:
            List of dictionaries with key health details
        """
        config = self.key_manager.config
        details = []
        
        for key in config.upstream_keys:
            # Calculate time since last failure
            time_since_failure = None
            if key.last_failed_time:
                from ..utils import time_since, format_duration
                elapsed = time_since(key.last_failed_time)
                time_since_failure = format_duration(elapsed.total_seconds())
            
            # Determine recovery status
            recovery_status = "not_needed"
            if key.permanently_disabled:
                recovery_status = "permanently_disabled"
            elif not key.enabled:
                if key.recovery_attempted:
                    recovery_status = "recovery_attempted"
                elif key.last_failed_time and is_expired(key.last_failed_time, settings.recovery_time):
                    recovery_status = "ready_for_recovery"
                else:
                    recovery_status = "waiting_for_recovery"
            
            details.append({
                "key_suffix": key.key[-4:] if len(key.key) >= 4 else key.key,
                "enabled": key.enabled,
                "permanently_disabled": key.permanently_disabled,
                "recovery_attempted": key.recovery_attempted,
                "failed_count": key.failed_count,
                "total_requests": key.total_requests,
                "last_failed_time": key.last_failed_time,
                "time_since_failure": time_since_failure,
                "recovery_status": recovery_status,
                "weight": key.weight,
                "description": key.description,
            })
        
        return details
    
    async def force_recovery_check(self) -> dict:
        """
        Force an immediate recovery check on all keys.
        
        Returns:
            Dictionary with recovery results
        """
        logger.info("Forcing immediate recovery check")
        
        config = self.key_manager.config
        results = {
            "checked": 0,
            "recovered": 0,
            "skipped": 0,
            "errors": 0,
        }
        
        for key in config.upstream_keys:
            try:
                results["checked"] += 1
                
                if await self.check_key_health(key):
                    results["recovered"] += 1
                else:
                    results["skipped"] += 1
                    
            except Exception as e:
                logger.error(f"Error checking key {key.masked_key}: {e}")
                results["errors"] += 1
        
        logger.info(
            f"Forced recovery check completed: {results['recovered']} recovered, "
            f"{results['skipped']} skipped, {results['errors']} errors"
        )
        
        return results
