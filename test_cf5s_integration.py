#!/usr/bin/env python3
"""
Test script for CF5S + WARP integration.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.cf5s_client import CF5SClient, CF5SPlaywrightIntegration
from playwright.async_api import async_playwright


async def test_cf5s_integration():
    """Test CF5S + WARP integration."""
    
    print("🚀 Starting CF5S + WARP integration test...")
    
    # Test CF5S client
    cf5s_gateway_url = "http://localhost:8080"  # Adjust if different
    proxy_url = "socks5://localhost:1080"       # Adjust if different
    
    try:
        async with CF5SClient(cf5s_gateway_url, proxy_url) as cf5s_client:
            print("✅ CF5S client created successfully")
            
            # Test CF5S bypass
            print("🔍 Testing CF5S bypass...")
            if await cf5s_client.test_bypass():
                print("✅ CF5S bypass test successful")
                
                # Test with Playwright integration
                print("🎭 Testing Playwright integration...")
                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)
                    context = await browser.new_context()
                    page = await context.new_page()
                    
                    # Setup CF5S integration
                    cf5s_integration = CF5SPlaywrightIntegration(cf5s_client)
                    
                    # Test navigation
                    if await cf5s_integration.navigate_with_bypass(page, "https://auth.ampcode.com/sign-up"):
                        print("✅ CF5S + Playwright navigation successful")
                        
                        # Take screenshot
                        await page.screenshot(path="cf5s_integration_success.png")
                        print("📸 Screenshot saved: cf5s_integration_success.png")
                        
                        # Get page info
                        title = await page.title()
                        url = page.url
                        print(f"📋 Page title: {title}")
                        print(f"🔗 Current URL: {url}")
                        
                    else:
                        print("❌ CF5S + Playwright navigation failed")
                        await page.screenshot(path="cf5s_integration_error.png")
                        print("📸 Error screenshot saved: cf5s_integration_error.png")
                    
                    await browser.close()
                    
            else:
                print("❌ CF5S bypass test failed")
                
    except Exception as e:
        print(f"❌ CF5S integration test failed: {e}")
    
    print("🏁 CF5S integration test completed!")


async def test_warp_connectivity():
    """Test WARP proxy connectivity."""
    
    print("🌐 Testing WARP proxy connectivity...")
    
    try:
        import httpx
        
        # Test WARP proxy
        proxy_url = "socks5://localhost:1080"
        
        async with httpx.AsyncClient(
            proxies={
                "http://": proxy_url,
                "https://": proxy_url
            },
            timeout=30.0
        ) as client:
            
            # Test basic connectivity
            response = await client.get("https://httpbin.org/ip")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ WARP proxy working, IP: {data.get('origin', 'Unknown')}")
                return True
            else:
                print(f"❌ WARP proxy test failed with status: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ WARP proxy test error: {e}")
        return False


async def main():
    """Main test function."""
    
    print("🧪 Starting CF5S + WARP integration tests...")
    print("=" * 50)
    
    # Test WARP connectivity first
    warp_ok = await test_warp_connectivity()
    print()
    
    if warp_ok:
        # Test CF5S integration
        await test_cf5s_integration()
    else:
        print("⚠️  Skipping CF5S test due to WARP connectivity issues")
        print("💡 Make sure WARP container is running: docker-compose up warp")
    
    print("=" * 50)
    print("✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
