"""
API request and response models.

This module contains data models for API requests, responses, and related structures
used in the Anthropic API proxy communication.
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import Field, validator

from .base import BaseConfigModel


class AnthropicModel(BaseConfigModel):
    """Model representing an available Anthropic AI model."""
    
    id: str = Field(..., description="Model identifier")
    object: str = Field("model", description="Object type (always 'model')")
    owned_by: str = Field("anthropic", description="Model owner")
    created: Optional[int] = Field(None, description="Creation timestamp")
    
    @validator('id')
    def validate_model_id(cls, v):
        """Validate model ID format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Model ID cannot be empty")
        return v.strip()


class ModelsListResponse(BaseConfigModel):
    """Response model for the models list endpoint."""
    
    object: str = Field("list", description="Object type")
    data: List[AnthropicModel] = Field(..., description="List of available models")


class MessageContent(BaseConfigModel):
    """Content of a message in the conversation."""
    
    type: str = Field(..., description="Content type (e.g., 'text')")
    text: Optional[str] = Field(None, description="Text content")


class Message(BaseConfigModel):
    """A message in the conversation."""
    
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: Union[str, List[MessageContent]] = Field(..., description="Message content")
    
    @validator('role')
    def validate_role(cls, v):
        """Validate message role."""
        allowed_roles = {'user', 'assistant', 'system'}
        if v not in allowed_roles:
            raise ValueError(f"Role must be one of {allowed_roles}")
        return v


class MessagesRequest(BaseConfigModel):
    """Request model for the messages endpoint."""
    
    model: str = Field(..., description="Model to use for the request")
    messages: List[Message] = Field(..., description="List of messages in the conversation")
    max_tokens: int = Field(..., ge=1, le=4096, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top-p sampling parameter")
    top_k: Optional[int] = Field(None, ge=1, description="Top-k sampling parameter")
    stream: bool = Field(False, description="Whether to stream the response")
    stop_sequences: Optional[List[str]] = Field(None, description="Stop sequences")
    
    @validator('messages')
    def validate_messages_not_empty(cls, v):
        """Ensure messages list is not empty."""
        if not v:
            raise ValueError("Messages list cannot be empty")
        return v
    
    @validator('model')
    def validate_model_not_empty(cls, v):
        """Ensure model is not empty."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Model cannot be empty")
        return v.strip()


class Usage(BaseConfigModel):
    """Token usage information."""
    
    input_tokens: int = Field(..., ge=0, description="Number of input tokens")
    output_tokens: int = Field(..., ge=0, description="Number of output tokens")
    total_tokens: int = Field(..., ge=0, description="Total number of tokens")
    
    @validator('total_tokens', always=True)
    def validate_total_tokens(cls, v, values):
        """Ensure total tokens equals input + output tokens."""
        input_tokens = values.get('input_tokens', 0)
        output_tokens = values.get('output_tokens', 0)
        expected_total = input_tokens + output_tokens
        if v != expected_total:
            return expected_total  # Auto-correct the total
        return v


class MessagesResponse(BaseConfigModel):
    """Response model for the messages endpoint."""
    
    id: str = Field(..., description="Response ID")
    type: str = Field("message", description="Response type")
    role: str = Field("assistant", description="Response role")
    content: List[MessageContent] = Field(..., description="Response content")
    model: str = Field(..., description="Model used for the response")
    stop_reason: Optional[str] = Field(None, description="Reason for stopping generation")
    stop_sequence: Optional[str] = Field(None, description="Stop sequence that triggered stopping")
    usage: Usage = Field(..., description="Token usage information")


class StreamChunk(BaseConfigModel):
    """A chunk in a streaming response."""
    
    type: str = Field(..., description="Chunk type")
    delta: Optional[Dict[str, Any]] = Field(None, description="Delta content")
    message: Optional[Dict[str, Any]] = Field(None, description="Message content")
    usage: Optional[Usage] = Field(None, description="Usage information")


class ProxyRequest(BaseConfigModel):
    """Internal model for proxy request tracking."""
    
    client_key: str = Field(..., description="Client key used for the request")
    upstream_key: str = Field(..., description="Upstream key used for the request")
    request_data: Dict[str, Any] = Field(..., description="Original request data")
    is_streaming: bool = Field(False, description="Whether this is a streaming request")
    retry_count: int = Field(0, ge=0, description="Number of retries attempted")
    start_time: float = Field(..., description="Request start timestamp")
    
    @property
    def masked_client_key(self) -> str:
        """Get masked client key for logging."""
        if len(self.client_key) <= 4:
            return "****"
        return f"...{self.client_key[-4:]}"
    
    @property
    def masked_upstream_key(self) -> str:
        """Get masked upstream key for logging."""
        if len(self.upstream_key) <= 4:
            return "****"
        return f"...{self.upstream_key[-4:]}"


class ProxyResponse(BaseConfigModel):
    """Internal model for proxy response tracking."""
    
    status_code: int = Field(..., description="HTTP status code")
    content: bytes = Field(..., description="Response content")
    headers: Dict[str, str] = Field(..., description="Response headers")
    upstream_key: str = Field(..., description="Upstream key used")
    processing_time: float = Field(..., description="Processing time in seconds")
    success: bool = Field(..., description="Whether the request was successful")
    
    @property
    def masked_upstream_key(self) -> str:
        """Get masked upstream key for logging."""
        if len(self.upstream_key) <= 4:
            return "****"
        return f"...{self.upstream_key[-4:]}"


class ErrorResponse(BaseConfigModel):
    """Standard error response model."""
    
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    code: Optional[str] = Field(None, description="Error code")
    type: Optional[str] = Field(None, description="Error type")
    
    @classmethod
    def from_exception(cls, exc: Exception, detail: Optional[str] = None) -> "ErrorResponse":
        """Create an error response from an exception."""
        return cls(
            error=str(exc),
            detail=detail,
            type=type(exc).__name__
        )
