# Anthropic API Proxy Server (AMP 0.3)

[![Version](https://img.shields.io/badge/version-4.7.0-blue.svg)](https://github.com/your-repo/amp)
[![Python](https://img.shields.io/badge/python-3.8+-green.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-red.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-yellow.svg)](LICENSE)

A **production-ready**, **high-performance** proxy server for Anthropic's Claude API with enterprise-grade features including advanced key management, intelligent load balancing, automatic failure recovery, and comprehensive monitoring.

## 🚀 Key Features

### 🔐 **Advanced Key Management**
- **Strict Key Validation**: Comprehensive validation and sanitization of API keys
- **Multi-Key Support**: Manage multiple upstream and client keys simultaneously
- **Automatic Failure Detection**: Real-time monitoring with configurable failure thresholds
- **Smart Recovery**: Automatic key recovery with exponential backoff and retry logic
- **Key Rotation**: Hot-swappable keys without service interruption

### ⚖️ **Intelligent Load Balancing**
- **Multiple Strategies**: Round-robin, weighted round-robin, random, least-used, least-failed
- **Dynamic Strategy Switching**: Change strategies at runtime via API
- **Weight-Based Distribution**: Fine-tune traffic distribution with key weights
- **Performance Optimization**: Automatic selection of best-performing keys

### 🔄 **Reliability & Resilience**
- **Auto Retry Logic**: Configurable retry attempts with intelligent backoff
- **Health Monitoring**: Continuous background health checks and recovery
- **Graceful Degradation**: Seamless handling of key failures
- **Circuit Breaker Pattern**: Prevents cascade failures

### 📊 **Monitoring & Observability**
- **Real-time Statistics**: Comprehensive metrics on key usage and performance
- **Health Dashboards**: Detailed health status and system information
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Admin API**: RESTful management interface

### 🏗️ **Modern Architecture**
- **Modular Design**: Clean separation of concerns with dependency injection
- **Type Safety**: Full type annotations with Pydantic models
- **Async/Await**: High-performance asynchronous request handling
- **Configuration Management**: Environment-based configuration with validation
- **Comprehensive Testing**: 80%+ test coverage with unit and integration tests

## 📋 Requirements

- **Python**: 3.8 or higher
- **Dependencies**: FastAPI, Uvicorn, httpx, Pydantic
- **Memory**: Minimum 512MB RAM
- **Storage**: 100MB for application and logs

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/your-repo/amp.git
cd amp

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Create your configuration files:

**keys.json** (API Keys Configuration):
```json
{
  "upstream_keys": [
    {
      "key": "sgamp_user_01JYKDQX9Z2VMT6ND78S86RQPV_your_key_here",
      "weight": 2,
      "enabled": true,
      "description": "Primary production key"
    },
    {
      "key": "sgamp_user_01JYKDQX9Z2VMT6ND78S86RQPV_backup_key",
      "weight": 1,
      "enabled": true,
      "description": "Backup key"
    }
  ],
  "client_keys": [
    {
      "key": "client_production_key_12345",
      "enabled": true,
      "description": "Production client access",
      "rate_limit": 1000
    },
    {
      "key": "client_development_key_67890",
      "enabled": true,
      "description": "Development access",
      "rate_limit": 100
    }
  ]
}
```

**models.json** (Available Models):
```json
[
  {
    "id": "claude-3-7-sonnet-20250219",
    "object": "model",
    "owned_by": "anthropic"
  },
  {
    "id": "claude-sonnet-4-20250514",
    "object": "model", 
    "owned_by": "anthropic"
  }
]
```

### 3. Environment Configuration

Create a `.env` file:
```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# API Configuration
TARGET_API_URL=https://ampcode.com/api/provider/anthropic/v1/messages
ANTHROPIC_VERSION=2023-06-01

# Retry & Timeout Settings
MAX_RETRIES=3
REQUEST_TIMEOUT=120
CONNECT_TIMEOUT=10

# Health Monitoring
HEALTH_CHECK_INTERVAL=300
FAILURE_THRESHOLD=3
RECOVERY_TIME=300

# Load Balancing
DEFAULT_LOAD_BALANCER_STRATEGY=round_robin

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/amp.log
```

### 4. Run the Server

```bash
# Using the run script (recommended)
python run.py

# Or using uvicorn directly
uvicorn src.main:app --host 0.0.0.0 --port 8000

# Development mode with auto-reload
python run.py --reload --debug --log-level DEBUG
```

## 📖 Usage Examples

### Basic Message Request

```bash
curl -X POST "http://localhost:8000/v1/messages" \
  -H "Authorization: Bearer your_client_key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you today?"
      }
    ],
    "max_tokens": 100
  }'
```

### Streaming Request

```bash
curl -X POST "http://localhost:8000/v1/messages" \
  -H "Authorization: Bearer your_client_key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {
        "role": "user", 
        "content": "Write a short story"
      }
    ],
    "max_tokens": 500,
    "stream": true
  }'
```

### Get System Statistics

```bash
curl -X GET "http://localhost:8000/admin/stats" \
  -H "Authorization: Bearer your_client_key"
```

### Change Load Balancing Strategy

```bash
curl -X POST "http://localhost:8000/admin/strategy" \
  -H "Authorization: Bearer your_client_key" \
  -H "Content-Type: application/json" \
  -d '{"strategy": "least_used"}'
```

## 🏗️ Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │───▶│   AMP Proxy     │───▶│ Anthropic API   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Admin Panel    │
                       └─────────────────┘
```

### Module Structure

```
src/
├── config/          # Configuration management
│   ├── settings.py  # Application settings
│   └── loader.py    # Configuration file loading
├── models/          # Data models and schemas
│   ├── base.py      # Base model classes
│   ├── keys.py      # Key management models
│   └── api.py       # API request/response models
├── key_manager/     # Key management system
│   ├── manager.py   # Main key manager
│   └── health.py    # Health monitoring
├── load_balancer/   # Load balancing strategies
│   ├── base.py      # Strategy base classes
│   ├── strategies.py # Strategy implementations
│   └── manager.py   # Strategy manager
├── routers/         # FastAPI route handlers
│   ├── core.py      # Core API endpoints
│   ├── admin.py     # Admin endpoints
│   ├── health.py    # Health check endpoints
│   └── dependencies.py # Shared dependencies
├── utils/           # Utility functions
│   ├── keys.py      # Key utilities
│   ├── validation.py # Validation functions
│   ├── logging.py   # Logging utilities
│   └── time.py      # Time utilities
└── main.py          # Application entry point
```

## 📚 API Reference

### Core Endpoints

#### POST /v1/messages
Proxy messages to Anthropic API with load balancing and retry logic.

**Headers:**
- `Authorization: Bearer <client_key>` (required)
- `Content-Type: application/json` (required)

**Request Body:**
```json
{
  "model": "claude-3-sonnet-20240229",
  "messages": [
    {
      "role": "user",
      "content": "Your message here"
    }
  ],
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

#### GET /v1/models
List available Anthropic models.

**Headers:**
- `Authorization: Bearer <client_key>` (required)

**Response:**
```json
{
  "object": "list",
  "data": [
    {
      "id": "claude-3-sonnet-20240229",
      "object": "model",
      "owned_by": "anthropic"
    }
  ]
}
```

### Admin Endpoints

#### GET /admin/stats
Get comprehensive system statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "load_balancer": {
      "strategy": "round_robin",
      "available_strategies": ["round_robin", "random", "least_used"]
    },
    "key_summary": {
      "total_upstream_keys": 3,
      "active_upstream_keys": 2,
      "temporarily_disabled_keys": 1,
      "permanently_disabled_keys": 0
    },
    "keys_details": [...]
  }
}
```

#### POST /admin/strategy
Change the load balancing strategy.

**Request Body:**
```json
{
  "strategy": "least_used"
}
```

#### GET /admin/keys/status
Get detailed status of all keys.

#### POST /admin/keys/reset
Reset a key's failure status.

**Request Body:**
```json
{
  "key_suffix": "abcd"
}
```

### Health Check Endpoints

#### GET /
Basic health check - no authentication required.

#### GET /health
Detailed health information - optional authentication for more details.

#### GET /status
Service status in standardized format.

#### GET /metrics
System metrics for monitoring integration.

## ⚙️ Configuration Reference

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server bind address |
| `PORT` | `8000` | Server port |
| `DEBUG` | `false` | Enable debug mode |
| `LOG_LEVEL` | `INFO` | Logging level |
| `TARGET_API_URL` | Required | Anthropic API endpoint |
| `MAX_RETRIES` | `3` | Maximum retry attempts |
| `REQUEST_TIMEOUT` | `120` | Request timeout (seconds) |
| `HEALTH_CHECK_INTERVAL` | `300` | Health check interval (seconds) |
| `FAILURE_THRESHOLD` | `3` | Failures before key disable |
| `RECOVERY_TIME` | `300` | Recovery wait time (seconds) |

### Load Balancing Strategies

| Strategy | Description | Use Case |
|----------|-------------|----------|
| `round_robin` | Cycles through keys in order | Equal distribution |
| `weighted_round_robin` | Considers key weights | Unequal capacity keys |
| `random` | Random selection | Simple load distribution |
| `weighted_random` | Weighted random selection | Probabilistic distribution |
| `least_used` | Selects least used key | Balance usage counts |
| `least_failed` | Selects most reliable key | Prioritize reliability |

## 🧪 Testing

### Run Tests

```bash
# Run all tests
python test_runner.py

# Run specific test types
python test_runner.py --type unit
python test_runner.py --type integration

# Run with coverage
python test_runner.py --type all

# Run linting
python test_runner.py --lint

# Fix formatting
python test_runner.py --fix
```

### Test Structure

```
tests/
├── conftest.py           # Test configuration and fixtures
├── test_models.py        # Data model tests
├── test_utils.py         # Utility function tests
├── test_load_balancer.py # Load balancer tests
├── test_key_manager.py   # Key manager tests
├── test_config.py        # Configuration tests
├── test_routers.py       # API endpoint tests
└── test_integration.py   # Integration tests
```

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY run.py .

EXPOSE 8000
CMD ["python", "run.py"]
```

### Production Considerations

1. **Security**:
   - Use HTTPS in production
   - Secure API key storage
   - Regular key rotation
   - Network security groups

2. **Performance**:
   - Use multiple worker processes
   - Configure appropriate timeouts
   - Monitor memory usage
   - Set up load balancing

3. **Monitoring**:
   - Set up log aggregation
   - Configure health checks
   - Monitor key usage patterns
   - Set up alerting

4. **Backup**:
   - Regular configuration backups
   - Key management procedures
   - Disaster recovery plan

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run tests before committing
python test_runner.py --all
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-repo/amp/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-repo/amp/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/amp/discussions)

## 🙏 Acknowledgments

- [Anthropic](https://anthropic.com) for the Claude API
- [FastAPI](https://fastapi.tiangolo.com) for the excellent web framework
- [Pydantic](https://pydantic-docs.helpmanual.io) for data validation
- The open-source community for inspiration and tools
