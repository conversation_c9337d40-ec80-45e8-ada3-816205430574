#!/usr/bin/env python3
"""
Test script for Cloudflare bypass functionality.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from playwright.async_api import async_playwright
from src.utils.cf_bypass import CloudflareBypass


async def test_cf_bypass():
    """Test Cloudflare bypass functionality."""
    
    cf_bypass = CloudflareBypass()
    
    async with async_playwright() as p:
        print("🚀 Starting Cloudflare bypass test...")
        
        # Create stealth browser
        browser = await cf_bypass.create_stealth_browser(p, headless=True)
        context = await cf_bypass.create_stealth_context(browser)
        page = await context.new_page()
        
        # Setup stealth page
        await cf_bypass.setup_stealth_page(page)
        
        try:
            print("🔍 Testing navigation to registration page...")
            
            # Test navigation with CF bypass
            success = await cf_bypass.navigate_with_retry(
                page, 
                "https://auth.ampcode.com/sign-up",
                max_retries=3
            )
            
            if success:
                print("✅ Successfully navigated to registration page!")
                
                # Take screenshot
                await page.screenshot(path="cf_bypass_success.png", full_page=True)
                print("📸 Screenshot saved: cf_bypass_success.png")
                
                # Get page info
                title = await page.title()
                url = page.url
                print(f"📋 Page title: {title}")
                print(f"🔗 Current URL: {url}")
                
                # Check for form elements
                input_count = await page.locator('input').count()
                button_count = await page.locator('button').count()
                form_count = await page.locator('form').count()
                
                print(f"📊 Found {input_count} inputs, {button_count} buttons, {form_count} forms")
                
                # Test specific selectors
                selectors = [
                    'input[name="first_name"]',
                    'input[name="firstName"]',
                    'input[name="email"]',
                    'input[type="email"]'
                ]
                
                for selector in selectors:
                    count = await page.locator(selector).count()
                    if count > 0:
                        print(f"✅ Found {count} element(s) with selector: {selector}")
                    else:
                        print(f"❌ No elements found with selector: {selector}")
                
                # Test human-like typing if we find an input
                if input_count > 0:
                    print("🧪 Testing human-like typing...")
                    try:
                        first_input = page.locator('input').first
                        await cf_bypass.human_like_typing(page, 'input', "Test")
                        print("✅ Human-like typing test successful")
                    except Exception as e:
                        print(f"⚠️  Human-like typing test failed: {e}")
                
            else:
                print("❌ Failed to navigate to registration page")
                
                # Take error screenshot
                await page.screenshot(path="cf_bypass_error.png", full_page=True)
                print("📸 Error screenshot saved: cf_bypass_error.png")
                
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            
            # Take error screenshot
            try:
                await page.screenshot(path="cf_bypass_exception.png", full_page=True)
                print("📸 Exception screenshot saved: cf_bypass_exception.png")
            except:
                pass
                
        finally:
            await browser.close()
            print("🏁 Test completed!")


if __name__ == "__main__":
    print("🚀 Starting Cloudflare bypass test...")
    asyncio.run(test_cf_bypass())
    print("✅ Test finished!")
