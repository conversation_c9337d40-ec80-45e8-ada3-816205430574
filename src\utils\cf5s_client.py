"""
CF5S client for bypassing Cloudflare protection.
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any

import httpx
from playwright.async_api import Page, <PERSON><PERSON><PERSON>, <PERSON>rowserContext

from . import get_logger

logger = get_logger(__name__)


class CF5SClient:
    """
    CF5S client for bypassing Cloudflare protection using external CF5S service.
    """
    
    def __init__(self, cf5s_gateway_url: str = "http://cf5s:8080", proxy_url: str = "socks5://warp:1080"):
        """
        Initialize CF5S client.
        
        Args:
            cf5s_gateway_url: CF5S gateway URL
            proxy_url: WARP proxy URL
        """
        self.cf5s_gateway_url = cf5s_gateway_url.rstrip('/')
        self.proxy_url = proxy_url
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()
        
    async def start_session(self):
        """Start HTTP session with proxy."""
        try:
            # Configure proxy for httpx
            proxy_config = None
            if self.proxy_url:
                proxy_config = {
                    "http://": self.proxy_url,
                    "https://": self.proxy_url
                }
            
            self.session = httpx.AsyncClient(
                proxies=proxy_config,
                timeout=httpx.Timeout(60.0),
                follow_redirects=True,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            )
            logger.info("CF5S client session started with proxy")
            
        except Exception as e:
            logger.error(f"Failed to start CF5S session: {e}")
            raise
            
    async def close_session(self):
        """Close HTTP session."""
        if self.session:
            await self.session.aclose()
            self.session = None
            logger.info("CF5S client session closed")
    
    async def bypass_cloudflare(self, url: str, max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        Bypass Cloudflare protection for a given URL.
        
        Args:
            url: Target URL to bypass
            max_retries: Maximum retry attempts
            
        Returns:
            Dict containing bypass result or None if failed
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting CF5S bypass for {url} (attempt {attempt + 1}/{max_retries})")
                
                # Request CF5S to bypass the URL
                bypass_request = {
                    "url": url,
                    "method": "GET",
                    "headers": {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Accept-Encoding": "gzip, deflate, br",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    }
                }
                
                response = await self.session.post(
                    f"{self.cf5s_gateway_url}/bypass",
                    json=bypass_request,
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"CF5S bypass successful for {url}")
                    return result
                else:
                    logger.warning(f"CF5S bypass failed with status {response.status_code}: {response.text}")
                    
            except Exception as e:
                logger.warning(f"CF5S bypass attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    
        logger.error(f"CF5S bypass failed for {url} after {max_retries} attempts")
        return None
    
    async def get_bypassed_cookies(self, url: str) -> Optional[Dict[str, str]]:
        """
        Get cookies after bypassing Cloudflare.
        
        Args:
            url: Target URL
            
        Returns:
            Dict of cookies or None if failed
        """
        try:
            result = await self.bypass_cloudflare(url)
            if result and "cookies" in result:
                return result["cookies"]
            return None
            
        except Exception as e:
            logger.error(f"Failed to get bypassed cookies: {e}")
            return None
    
    async def create_bypassed_session(self, url: str) -> Optional[httpx.AsyncClient]:
        """
        Create an HTTP session with bypassed cookies.
        
        Args:
            url: Target URL to bypass
            
        Returns:
            httpx.AsyncClient with bypassed cookies or None if failed
        """
        try:
            result = await self.bypass_cloudflare(url)
            if not result:
                return None
                
            # Extract cookies and headers from bypass result
            cookies = result.get("cookies", {})
            headers = result.get("headers", {})
            
            # Create new session with bypassed data
            proxy_config = None
            if self.proxy_url:
                proxy_config = {
                    "http://": self.proxy_url,
                    "https://": self.proxy_url
                }
            
            session = httpx.AsyncClient(
                proxies=proxy_config,
                timeout=httpx.Timeout(60.0),
                follow_redirects=True,
                cookies=cookies,
                headers={
                    **headers,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            )
            
            logger.info("Created bypassed HTTP session")
            return session
            
        except Exception as e:
            logger.error(f"Failed to create bypassed session: {e}")
            return None
    
    async def test_bypass(self) -> bool:
        """
        Test CF5S bypass functionality.
        
        Returns:
            True if bypass is working, False otherwise
        """
        try:
            logger.info("Testing CF5S bypass functionality...")
            
            # Test with a known Cloudflare-protected site
            test_url = "https://auth.ampcode.com/sign-up"
            result = await self.bypass_cloudflare(test_url)
            
            if result and result.get("success", False):
                logger.info("CF5S bypass test successful")
                return True
            else:
                logger.warning("CF5S bypass test failed")
                return False
                
        except Exception as e:
            logger.error(f"CF5S bypass test error: {e}")
            return False


class CF5SPlaywrightIntegration:
    """
    Integration between CF5S and Playwright for browser automation.
    """
    
    def __init__(self, cf5s_client: CF5SClient):
        """
        Initialize CF5S Playwright integration.
        
        Args:
            cf5s_client: CF5S client instance
        """
        self.cf5s_client = cf5s_client
        
    async def setup_bypassed_page(self, page: Page, url: str) -> bool:
        """
        Setup Playwright page with CF5S bypassed cookies and headers.
        
        Args:
            page: Playwright page instance
            url: Target URL to bypass
            
        Returns:
            True if setup successful, False otherwise
        """
        try:
            logger.info(f"Setting up bypassed page for {url}")
            
            # Get bypass result from CF5S
            result = await self.cf5s_client.bypass_cloudflare(url)
            if not result:
                logger.error("Failed to get CF5S bypass result")
                return False
            
            # Extract cookies and headers
            cookies = result.get("cookies", {})
            headers = result.get("headers", {})
            
            # Set cookies in browser context
            if cookies:
                cookie_list = []
                for name, value in cookies.items():
                    cookie_list.append({
                        "name": name,
                        "value": value,
                        "domain": ".ampcode.com",
                        "path": "/"
                    })
                
                await page.context.add_cookies(cookie_list)
                logger.info(f"Added {len(cookie_list)} bypassed cookies")
            
            # Set extra headers
            if headers:
                await page.set_extra_http_headers(headers)
                logger.info(f"Set {len(headers)} bypassed headers")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup bypassed page: {e}")
            return False
    
    async def navigate_with_bypass(self, page: Page, url: str, timeout: int = 60000) -> bool:
        """
        Navigate to URL using CF5S bypass.
        
        Args:
            page: Playwright page instance
            url: Target URL
            timeout: Navigation timeout in milliseconds
            
        Returns:
            True if navigation successful, False otherwise
        """
        try:
            logger.info(f"Navigating to {url} with CF5S bypass")
            
            # Setup bypassed page first
            if not await self.setup_bypassed_page(page, url):
                logger.error("Failed to setup bypassed page")
                return False
            
            # Navigate to the URL
            response = await page.goto(url, timeout=timeout, wait_until="domcontentloaded")
            
            if response and response.status < 400:
                logger.info(f"Successfully navigated to {url} with status {response.status}")
                return True
            else:
                logger.warning(f"Navigation failed with status {response.status if response else 'None'}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to navigate with CF5S bypass: {e}")
            return False
