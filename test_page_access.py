#!/usr/bin/env python3
"""
Simple test script to verify page access and basic functionality.
"""

import asyncio
import time
from playwright.async_api import async_playwright


async def test_page_access():
    """Test basic page access."""
    
    async with async_playwright() as p:
        # Launch browser in headless mode (Linux server compatible)
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        
        try:
            print("🔍 Testing page access...")
            
            # Test basic connectivity
            print("📄 Loading registration page...")
            response = await page.goto("https://auth.ampcode.com/sign-up", timeout=90000)
            print(f"✅ Page loaded with status: {response.status}")
            
            # Wait for page to load
            await asyncio.sleep(10)
            
            # Get basic page info
            title = await page.title()
            url = page.url
            print(f"📋 Page title: {title}")
            print(f"🔗 Current URL: {url}")
            
            # Take screenshot
            screenshot_path = f"test_page_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Check for basic elements
            input_count = await page.locator('input').count()
            button_count = await page.locator('button').count()
            form_count = await page.locator('form').count()
            
            print(f"📊 Found {input_count} inputs, {button_count} buttons, {form_count} forms")
            
            # Test specific selectors
            selectors = [
                'input[name="first_name"]',
                'input[name="firstName"]',
                'input[name="email"]',
                'input[type="email"]'
            ]
            
            for selector in selectors:
                count = await page.locator(selector).count()
                if count > 0:
                    print(f"✅ Found {count} element(s) with selector: {selector}")
                else:
                    print(f"❌ No elements found with selector: {selector}")
            
            print("✅ Test completed successfully!")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            
            # Take error screenshot
            try:
                error_screenshot = f"test_error_{int(time.time())}.png"
                await page.screenshot(path=error_screenshot)
                print(f"📸 Error screenshot saved: {error_screenshot}")
            except:
                pass
                
        finally:
            await browser.close()


if __name__ == "__main__":
    print("🚀 Starting page access test...")
    asyncio.run(test_page_access())
    print("🏁 Test completed!")
