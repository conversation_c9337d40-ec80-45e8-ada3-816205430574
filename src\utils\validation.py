"""
Validation utility functions.

This module contains various validation functions used throughout the application
for validating requests, responses, and data structures.
"""

import re
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from ..models import MessagesRequest


def validate_url(url: str) -> bool:
    """
    Validate that a string is a valid URL.
    
    Args:
        url: URL string to validate
        
    Returns:
        True if URL is valid, False otherwise
        
    Examples:
        >>> validate_url("https://api.anthropic.com/v1/messages")
        True
        >>> validate_url("not-a-url")
        False
        >>> validate_url("")
        False
    """
    if not url or not isinstance(url, str):
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_http_method(method: str) -> bool:
    """
    Validate that a string is a valid HTTP method.
    
    Args:
        method: HTTP method string to validate
        
    Returns:
        True if method is valid, False otherwise
        
    Examples:
        >>> validate_http_method("GET")
        True
        >>> validate_http_method("POST")
        True
        >>> validate_http_method("INVALID")
        False
    """
    if not method or not isinstance(method, str):
        return False
    
    valid_methods = {
        'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 
        'HEAD', 'OPTIONS', 'TRACE', 'CONNECT'
    }
    
    return method.upper() in valid_methods


def validate_content_type(content_type: str) -> bool:
    """
    Validate that a string is a valid content type.
    
    Args:
        content_type: Content type string to validate
        
    Returns:
        True if content type is valid, False otherwise
        
    Examples:
        >>> validate_content_type("application/json")
        True
        >>> validate_content_type("text/plain")
        True
        >>> validate_content_type("invalid")
        False
    """
    if not content_type or not isinstance(content_type, str):
        return False
    
    # Basic content type pattern: type/subtype
    pattern = r'^[a-zA-Z][a-zA-Z0-9][a-zA-Z0-9\-\.]*/[a-zA-Z0-9][a-zA-Z0-9\-\.]*$'
    return bool(re.match(pattern, content_type.split(';')[0].strip()))


def validate_json_structure(data: Any, required_fields: List[str]) -> bool:
    """
    Validate that a JSON structure contains required fields.
    
    Args:
        data: Data structure to validate
        required_fields: List of required field names
        
    Returns:
        True if all required fields are present, False otherwise
        
    Examples:
        >>> validate_json_structure({"name": "test", "value": 123}, ["name", "value"])
        True
        >>> validate_json_structure({"name": "test"}, ["name", "value"])
        False
    """
    if not isinstance(data, dict):
        return False
    
    return all(field in data for field in required_fields)


def validate_anthropic_request(request_data: Dict[str, Any]) -> tuple[bool, Optional[str]]:
    """
    Validate an Anthropic API request structure.
    
    Args:
        request_data: Request data to validate
        
    Returns:
        Tuple of (is_valid, error_message)
        
    Examples:
        >>> validate_anthropic_request({
        ...     "model": "claude-3-sonnet",
        ...     "messages": [{"role": "user", "content": "Hello"}],
        ...     "max_tokens": 100
        ... })
        (True, None)
        >>> validate_anthropic_request({"model": "claude-3-sonnet"})
        (False, "Missing required field: messages")
    """
    if not isinstance(request_data, dict):
        return False, "Request data must be a dictionary"
    
    # Check required fields
    required_fields = ["model", "messages", "max_tokens"]
    for field in required_fields:
        if field not in request_data:
            return False, f"Missing required field: {field}"
    
    # Validate model
    if not isinstance(request_data["model"], str) or not request_data["model"].strip():
        return False, "Model must be a non-empty string"
    
    # Validate messages
    messages = request_data["messages"]
    if not isinstance(messages, list) or not messages:
        return False, "Messages must be a non-empty list"
    
    for i, message in enumerate(messages):
        if not isinstance(message, dict):
            return False, f"Message {i} must be a dictionary"
        
        if "role" not in message or "content" not in message:
            return False, f"Message {i} must have 'role' and 'content' fields"
        
        if message["role"] not in ["user", "assistant", "system"]:
            return False, f"Message {i} has invalid role: {message['role']}"
    
    # Validate max_tokens
    max_tokens = request_data["max_tokens"]
    if not isinstance(max_tokens, int) or max_tokens <= 0:
        return False, "max_tokens must be a positive integer"
    
    # Validate optional fields
    if "temperature" in request_data:
        temp = request_data["temperature"]
        if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
            return False, "temperature must be a number between 0 and 2"
    
    if "top_p" in request_data:
        top_p = request_data["top_p"]
        if not isinstance(top_p, (int, float)) or top_p < 0 or top_p > 1:
            return False, "top_p must be a number between 0 and 1"
    
    if "stream" in request_data:
        if not isinstance(request_data["stream"], bool):
            return False, "stream must be a boolean"
    
    return True, None


def validate_model_id(model_id: str) -> bool:
    """
    Validate that a model ID has a valid format.
    
    Args:
        model_id: Model ID to validate
        
    Returns:
        True if model ID is valid, False otherwise
        
    Examples:
        >>> validate_model_id("claude-3-sonnet-20240229")
        True
        >>> validate_model_id("claude-3-haiku")
        True
        >>> validate_model_id("")
        False
        >>> validate_model_id("invalid model id")
        False
    """
    if not model_id or not isinstance(model_id, str):
        return False
    
    # Model ID should not contain spaces and should be reasonable length
    if ' ' in model_id or len(model_id) < 3 or len(model_id) > 100:
        return False
    
    # Should contain only alphanumeric characters, hyphens, and underscores
    pattern = r'^[a-zA-Z0-9\-_]+$'
    return bool(re.match(pattern, model_id))


def validate_port_number(port: Union[str, int]) -> bool:
    """
    Validate that a port number is valid.
    
    Args:
        port: Port number to validate (string or integer)
        
    Returns:
        True if port is valid, False otherwise
        
    Examples:
        >>> validate_port_number(8000)
        True
        >>> validate_port_number("8080")
        True
        >>> validate_port_number(0)
        False
        >>> validate_port_number(70000)
        False
    """
    try:
        port_int = int(port)
        return 1 <= port_int <= 65535
    except (ValueError, TypeError):
        return False


def validate_timeout_value(timeout: Union[str, int, float]) -> bool:
    """
    Validate that a timeout value is valid.
    
    Args:
        timeout: Timeout value to validate
        
    Returns:
        True if timeout is valid, False otherwise
        
    Examples:
        >>> validate_timeout_value(30.0)
        True
        >>> validate_timeout_value("60")
        True
        >>> validate_timeout_value(-1)
        False
        >>> validate_timeout_value("invalid")
        False
    """
    try:
        timeout_float = float(timeout)
        return timeout_float > 0 and timeout_float <= 3600  # Max 1 hour
    except (ValueError, TypeError):
        return False


def validate_weight_value(weight: Union[str, int]) -> bool:
    """
    Validate that a weight value is valid for load balancing.
    
    Args:
        weight: Weight value to validate
        
    Returns:
        True if weight is valid, False otherwise
        
    Examples:
        >>> validate_weight_value(1)
        True
        >>> validate_weight_value("5")
        True
        >>> validate_weight_value(0)
        False
        >>> validate_weight_value(101)
        False
    """
    try:
        weight_int = int(weight)
        return 1 <= weight_int <= 100
    except (ValueError, TypeError):
        return False


def validate_rate_limit(rate_limit: Union[str, int]) -> bool:
    """
    Validate that a rate limit value is valid.
    
    Args:
        rate_limit: Rate limit value to validate (requests per minute)
        
    Returns:
        True if rate limit is valid, False otherwise
        
    Examples:
        >>> validate_rate_limit(100)
        True
        >>> validate_rate_limit("60")
        True
        >>> validate_rate_limit(0)
        False
        >>> validate_rate_limit(10001)
        False
    """
    try:
        rate_int = int(rate_limit)
        return 1 <= rate_int <= 10000  # Max 10k requests per minute
    except (ValueError, TypeError):
        return False


def sanitize_string(value: str, max_length: int = 1000) -> str:
    """
    Sanitize a string by removing potentially harmful characters.
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
        
    Examples:
        >>> sanitize_string("Hello World!")
        "Hello World!"
        >>> sanitize_string("Test\\x00\\x01")
        "Test"
    """
    if not isinstance(value, str):
        return ""
    
    # Remove null bytes and control characters
    sanitized = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
    
    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized.strip()
