"""
Time-related utility functions.

This module provides utilities for working with time, timestamps,
and time-based calculations.
"""

import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Union


def get_current_timestamp() -> float:
    """
    Get the current timestamp as a float.
    
    Returns:
        Current timestamp in seconds since epoch
        
    Examples:
        >>> ts = get_current_timestamp()
        >>> isinstance(ts, float)
        True
    """
    return time.time()


def get_current_datetime() -> datetime:
    """
    Get the current datetime with timezone information.
    
    Returns:
        Current datetime with UTC timezone
        
    Examples:
        >>> dt = get_current_datetime()
        >>> dt.tzinfo is not None
        True
    """
    return datetime.now(timezone.utc)


def timestamp_to_datetime(timestamp: float) -> datetime:
    """
    Convert a timestamp to a datetime object.
    
    Args:
        timestamp: Timestamp in seconds since epoch
        
    Returns:
        Datetime object with UTC timezone
        
    Examples:
        >>> dt = timestamp_to_datetime(1640995200.0)
        >>> dt.year
        2022
    """
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> float:
    """
    Convert a datetime object to a timestamp.
    
    Args:
        dt: Datetime object
        
    Returns:
        Timestamp in seconds since epoch
        
    Examples:
        >>> dt = datetime(2022, 1, 1, tzinfo=timezone.utc)
        >>> ts = datetime_to_timestamp(dt)
        >>> ts == 1640995200.0
        True
    """
    return dt.timestamp()


def format_datetime(dt: datetime, format_string: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """
    Format a datetime object as a string.
    
    Args:
        dt: Datetime object to format
        format_string: Format string (default: ISO-like format)
        
    Returns:
        Formatted datetime string
        
    Examples:
        >>> dt = datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        >>> format_datetime(dt)
        '2022-01-01 12:00:00 UTC'
    """
    return dt.strftime(format_string)


def parse_datetime(date_string: str, format_string: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    Parse a datetime string into a datetime object.
    
    Args:
        date_string: String representation of datetime
        format_string: Format string to use for parsing
        
    Returns:
        Parsed datetime object with UTC timezone
        
    Examples:
        >>> dt = parse_datetime("2022-01-01 12:00:00")
        >>> dt.year
        2022
    """
    dt = datetime.strptime(date_string, format_string)
    return dt.replace(tzinfo=timezone.utc)


def time_since(dt: datetime) -> timedelta:
    """
    Calculate time elapsed since a given datetime.

    Args:
        dt: Datetime to calculate from

    Returns:
        Timedelta representing elapsed time

    Examples:
        >>> past = datetime.now(timezone.utc) - timedelta(hours=1)
        >>> elapsed = time_since(past)
        >>> elapsed.total_seconds() > 3500  # About 1 hour
        True
    """
    # Handle timezone-naive datetimes by assuming UTC
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    return get_current_datetime() - dt


def time_until(dt: datetime) -> timedelta:
    """
    Calculate time remaining until a given datetime.
    
    Args:
        dt: Target datetime
        
    Returns:
        Timedelta representing remaining time (negative if in the past)
        
    Examples:
        >>> future = datetime.now(timezone.utc) + timedelta(hours=1)
        >>> remaining = time_until(future)
        >>> remaining.total_seconds() > 3500  # About 1 hour
        True
    """
    return dt - get_current_datetime()


def is_expired(dt: datetime, expiry_seconds: float) -> bool:
    """
    Check if a datetime has expired based on a given expiry duration.
    
    Args:
        dt: Datetime to check
        expiry_seconds: Expiry duration in seconds
        
    Returns:
        True if expired, False otherwise
        
    Examples:
        >>> past = datetime.now(timezone.utc) - timedelta(hours=2)
        >>> is_expired(past, 3600)  # 1 hour expiry
        True
        >>> recent = datetime.now(timezone.utc) - timedelta(minutes=30)
        >>> is_expired(recent, 3600)  # 1 hour expiry
        False
    """
    elapsed = time_since(dt)
    return elapsed.total_seconds() > expiry_seconds


def add_seconds(dt: datetime, seconds: float) -> datetime:
    """
    Add seconds to a datetime.
    
    Args:
        dt: Base datetime
        seconds: Number of seconds to add
        
    Returns:
        New datetime with seconds added
        
    Examples:
        >>> dt = datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        >>> new_dt = add_seconds(dt, 3600)
        >>> new_dt.hour
        13
    """
    return dt + timedelta(seconds=seconds)


def subtract_seconds(dt: datetime, seconds: float) -> datetime:
    """
    Subtract seconds from a datetime.
    
    Args:
        dt: Base datetime
        seconds: Number of seconds to subtract
        
    Returns:
        New datetime with seconds subtracted
        
    Examples:
        >>> dt = datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        >>> new_dt = subtract_seconds(dt, 3600)
        >>> new_dt.hour
        11
    """
    return dt - timedelta(seconds=seconds)


def format_duration(seconds: float) -> str:
    """
    Format a duration in seconds as a human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
        
    Examples:
        >>> format_duration(3661)
        '1h 1m 1s'
        >>> format_duration(90)
        '1m 30s'
        >>> format_duration(45)
        '45s'
    """
    if seconds < 0:
        return "0s"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    parts = []
    if hours > 0:
        parts.append(f"{hours}h")
    if minutes > 0:
        parts.append(f"{minutes}m")
    if secs > 0 or not parts:
        parts.append(f"{secs}s")
    
    return " ".join(parts)


def format_relative_time(dt: datetime) -> str:
    """
    Format a datetime as a relative time string (e.g., "2 hours ago").
    
    Args:
        dt: Datetime to format
        
    Returns:
        Relative time string
        
    Examples:
        >>> past = datetime.now(timezone.utc) - timedelta(hours=2)
        >>> result = format_relative_time(past)
        >>> "ago" in result
        True
    """
    now = get_current_datetime()
    diff = now - dt
    
    if diff.total_seconds() < 0:
        # Future time
        diff = -diff
        suffix = "from now"
    else:
        suffix = "ago"
    
    total_seconds = diff.total_seconds()
    
    if total_seconds < 60:
        return f"{int(total_seconds)} seconds {suffix}"
    elif total_seconds < 3600:
        minutes = int(total_seconds // 60)
        return f"{minutes} minute{'s' if minutes != 1 else ''} {suffix}"
    elif total_seconds < 86400:
        hours = int(total_seconds // 3600)
        return f"{hours} hour{'s' if hours != 1 else ''} {suffix}"
    else:
        days = int(total_seconds // 86400)
        return f"{days} day{'s' if days != 1 else ''} {suffix}"


def sleep_until(target_time: datetime) -> None:
    """
    Sleep until a specific datetime.
    
    Args:
        target_time: Target datetime to sleep until
        
    Note:
        This function will return immediately if target_time is in the past.
        
    Examples:
        >>> future = datetime.now(timezone.utc) + timedelta(seconds=1)
        >>> sleep_until(future)  # Sleeps for about 1 second
    """
    remaining = time_until(target_time)
    if remaining.total_seconds() > 0:
        time.sleep(remaining.total_seconds())


class Timer:
    """
    A simple timer class for measuring elapsed time.
    
    Examples:
        >>> timer = Timer()
        >>> timer.start()
        >>> # ... do some work ...
        >>> elapsed = timer.elapsed()
        >>> timer.stop()
    """
    
    def __init__(self):
        """Initialize the timer."""
        self._start_time: Optional[float] = None
        self._end_time: Optional[float] = None
    
    def start(self) -> None:
        """Start the timer."""
        self._start_time = get_current_timestamp()
        self._end_time = None
    
    def stop(self) -> float:
        """
        Stop the timer and return elapsed time.
        
        Returns:
            Elapsed time in seconds
        """
        if self._start_time is None:
            raise ValueError("Timer not started")
        
        self._end_time = get_current_timestamp()
        return self._end_time - self._start_time
    
    def elapsed(self) -> float:
        """
        Get elapsed time without stopping the timer.
        
        Returns:
            Elapsed time in seconds
        """
        if self._start_time is None:
            raise ValueError("Timer not started")
        
        end_time = self._end_time or get_current_timestamp()
        return end_time - self._start_time
    
    def reset(self) -> None:
        """Reset the timer."""
        self._start_time = None
        self._end_time = None
    
    @property
    def is_running(self) -> bool:
        """Check if the timer is currently running."""
        return self._start_time is not None and self._end_time is None
