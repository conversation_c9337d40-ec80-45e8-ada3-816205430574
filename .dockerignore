# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
myenv/
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
.readthedocs.yml

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Screenshots and test artifacts
error_screenshot_*.png
*.png
*.jpg
*.jpeg

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Project specific
registered_accounts.json
REFACTOR_SUMMARY.md
LICENSE

# Backup files
*.bak
*.backup
*~

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
