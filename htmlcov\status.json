{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "38b1284591c9ce3cde9f00f48a461866", "files": {"z_145eef247bfb46b6___init___py": {"hash": "730585d0b4dee999151bf50839f55174", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997___init___py": {"hash": "f48db99a83059d90ad5691d1215695d6", "index": {"url": "z_b695c9c33e1e1997___init___py.html", "file": "src\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_loader_py": {"hash": "292e13090caf54bc735e1712c4a521e9", "index": {"url": "z_b695c9c33e1e1997_loader_py.html", "file": "src\\config\\loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_settings_py": {"hash": "6c87eab4244e344a1b7a524860c00373", "index": {"url": "z_b695c9c33e1e1997_settings_py.html", "file": "src\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4978456b28df205___init___py": {"hash": "87318d4dbc13ef6132d395bb62f3a032", "index": {"url": "z_b4978456b28df205___init___py.html", "file": "src\\key_manager\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4978456b28df205_health_py": {"hash": "7dda2ebddd385f022ab2952e78dde554", "index": {"url": "z_b4978456b28df205_health_py.html", "file": "src\\key_manager\\health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4978456b28df205_manager_py": {"hash": "e9e221af3d70b01b60fe7c2402e28c81", "index": {"url": "z_b4978456b28df205_manager_py.html", "file": "src\\key_manager\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cdc09ce95c6a37dc___init___py": {"hash": "dc431130b8e9e7c0de21198d9137f587", "index": {"url": "z_cdc09ce95c6a37dc___init___py.html", "file": "src\\load_balancer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cdc09ce95c6a37dc_base_py": {"hash": "f87c1dafcd7e091fd00c9072d34e1a35", "index": {"url": "z_cdc09ce95c6a37dc_base_py.html", "file": "src\\load_balancer\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cdc09ce95c6a37dc_manager_py": {"hash": "d71ad1e530a02689f99a746fc2a02149", "index": {"url": "z_cdc09ce95c6a37dc_manager_py.html", "file": "src\\load_balancer\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cdc09ce95c6a37dc_strategies_py": {"hash": "e952be9781b7906b45da2d34d894c32c", "index": {"url": "z_cdc09ce95c6a37dc_strategies_py.html", "file": "src\\load_balancer\\strategies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "b12e42830f1af4c4f59666fc8d58d739", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_532385f02888fdd3___init___py": {"hash": "06698e038783b347690367742cb309a1", "index": {"url": "z_532385f02888fdd3___init___py.html", "file": "src\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_532385f02888fdd3_api_py": {"hash": "a324c9895c390d9264d9053d867c4c7e", "index": {"url": "z_532385f02888fdd3_api_py.html", "file": "src\\models\\api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_532385f02888fdd3_base_py": {"hash": "c2b978ff7b2b9aecd63c668af2953593", "index": {"url": "z_532385f02888fdd3_base_py.html", "file": "src\\models\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_532385f02888fdd3_keys_py": {"hash": "52564025f886a392cd42e209d3bbca32", "index": {"url": "z_532385f02888fdd3_keys_py.html", "file": "src\\models\\keys.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ffb13e3a994f833___init___py": {"hash": "07810e276fdde5b57deff6df9ea0d5f6", "index": {"url": "z_9ffb13e3a994f833___init___py.html", "file": "src\\routers\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ffb13e3a994f833_admin_py": {"hash": "8c298bb242a56e0976ad5d7b441e400b", "index": {"url": "z_9ffb13e3a994f833_admin_py.html", "file": "src\\routers\\admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ffb13e3a994f833_core_py": {"hash": "9c3842f3fc13fb82dfb87f0f5a122437", "index": {"url": "z_9ffb13e3a994f833_core_py.html", "file": "src\\routers\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ffb13e3a994f833_dependencies_py": {"hash": "03c8788596b954380f561b763c241faa", "index": {"url": "z_9ffb13e3a994f833_dependencies_py.html", "file": "src\\routers\\dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ffb13e3a994f833_health_py": {"hash": "1545f8cddbb41157f0f19bc4c5fe69d8", "index": {"url": "z_9ffb13e3a994f833_health_py.html", "file": "src\\routers\\health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be___init___py": {"hash": "896e299ffeb9b825eb7990fc02cd39e9", "index": {"url": "z_6156a86a215061be___init___py.html", "file": "src\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_keys_py": {"hash": "2878147a617939651b07f8711d14060e", "index": {"url": "z_6156a86a215061be_keys_py.html", "file": "src\\utils\\keys.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_logging_py": {"hash": "e348144fd920020707427fe137b443ce", "index": {"url": "z_6156a86a215061be_logging_py.html", "file": "src\\utils\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_time_py": {"hash": "efd77982af7a1de799a3667371b86a57", "index": {"url": "z_6156a86a215061be_time_py.html", "file": "src\\utils\\time.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_validation_py": {"hash": "390711f2b42e42f88ca1f02465d7a3cf", "index": {"url": "z_6156a86a215061be_validation_py.html", "file": "src\\utils\\validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}