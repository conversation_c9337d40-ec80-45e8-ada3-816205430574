"""
Base classes and interfaces for load balancing strategies.

This module defines the abstract base class and common interfaces
for all load balancing strategies.
"""

from abc import ABC, abstractmethod
from typing import List, Tuple

from ..models import UpstreamKey


class LoadBalancerStrategy(ABC):
    """
    Abstract base class for load balancing strategies.
    
    All load balancing strategies must inherit from this class and implement
    the select_key method. This ensures a consistent interface across all strategies.
    """
    
    @abstractmethod
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Select a key from the list of available keys.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional strategy-specific parameters
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no suitable key can be selected
        """
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the strategy name."""
        pass
    
    @property
    def description(self) -> str:
        """Get a description of the strategy."""
        return f"{self.name} load balancing strategy"
    
    def filter_available_keys(self, keys: List[UpstreamKey]) -> List[UpstreamKey]:
        """
        Filter keys to only include those that are available for use.
        
        Args:
            keys: List of all upstream keys
            
        Returns:
            List of available keys (enabled and not permanently disabled)
            
        Raises:
            ValueError: If no keys are available
        """
        available_keys = [
            key for key in keys 
            if key.enabled and not key.permanently_disabled
        ]
        
        if not available_keys:
            raise ValueError("No available upstream keys")
        
        return available_keys
    
    def validate_keys(self, keys: List[UpstreamKey]) -> None:
        """
        Validate that the keys list is not empty and contains valid keys.
        
        Args:
            keys: List of keys to validate
            
        Raises:
            ValueError: If keys list is invalid
        """
        if not keys:
            raise ValueError("Keys list cannot be empty")
        
        if not all(isinstance(key, UpstreamKey) for key in keys):
            raise ValueError("All items in keys list must be UpstreamKey instances")


class StatefulStrategy(LoadBalancerStrategy):
    """
    Base class for strategies that maintain state between selections.
    
    Some strategies (like round-robin) need to maintain state to ensure
    proper distribution of requests across keys.
    """
    
    def __init__(self):
        """Initialize the stateful strategy."""
        self._state = {}
    
    def reset_state(self) -> None:
        """Reset the strategy state."""
        self._state = {}
    
    def get_state(self) -> dict:
        """Get the current strategy state."""
        return self._state.copy()
    
    def set_state(self, state: dict) -> None:
        """Set the strategy state."""
        self._state = state.copy()


class IndexBasedStrategy(StatefulStrategy):
    """
    Base class for strategies that use an index to track position.
    
    This is useful for round-robin and similar strategies that need to
    maintain a current position in the list of keys.
    """
    
    def __init__(self):
        """Initialize the index-based strategy."""
        super().__init__()
        self._state['current_index'] = 0
    
    @property
    def current_index(self) -> int:
        """Get the current index."""
        return self._state.get('current_index', 0)
    
    @current_index.setter
    def current_index(self, value: int) -> None:
        """Set the current index."""
        self._state['current_index'] = value
    
    def advance_index(self, max_index: int) -> int:
        """
        Advance the index and return the new value.
        
        Args:
            max_index: Maximum index value (exclusive)
            
        Returns:
            New index value
        """
        self.current_index = (self.current_index + 1) % max_index
        return self.current_index
    
    def get_current_and_advance(self, max_index: int) -> Tuple[int, int]:
        """
        Get the current index and advance it.
        
        Args:
            max_index: Maximum index value (exclusive)
            
        Returns:
            Tuple of (current_index, new_index)
        """
        current = self.current_index
        new_index = self.advance_index(max_index)
        return current, new_index
