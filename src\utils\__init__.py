"""
Utility functions module.

This package contains various utility functions used throughout the application,
organized by functional domain.
"""

from .keys import (
    compare_key_suffixes,
    extract_key_suffix,
    generate_key_id,
    is_key_similar,
    mask_key,
    normalize_key,
    sanitize_key_for_logging,
    validate_client_key_format,
    validate_key_format,
    validate_upstream_key_format,
)
from .logging import (
    ContextualLogger,
    get_logger,
    log_configuration_loaded,
    log_configuration_saved,
    log_error_with_context,
    log_health_check,
    log_key_event,
    log_request_end,
    log_request_start,
    log_strategy_change,
    setup_logging,
)
from .time import (
    Timer,
    add_seconds,
    datetime_to_timestamp,
    format_datetime,
    format_duration,
    format_relative_time,
    get_current_datetime,
    get_current_timestamp,
    is_expired,
    parse_datetime,
    sleep_until,
    subtract_seconds,
    time_since,
    time_until,
    timestamp_to_datetime,
)
from .validation import (
    sanitize_string,
    validate_anthropic_request,
    validate_content_type,
    validate_http_method,
    validate_json_structure,
    validate_model_id,
    validate_port_number,
    validate_rate_limit,
    validate_timeout_value,
    validate_url,
    validate_weight_value,
)

__all__ = [
    # Key utilities
    "compare_key_suffixes",
    "extract_key_suffix",
    "generate_key_id",
    "is_key_similar",
    "mask_key",
    "normalize_key",
    "sanitize_key_for_logging",
    "validate_client_key_format",
    "validate_key_format",
    "validate_upstream_key_format",
    # Logging utilities
    "ContextualLogger",
    "get_logger",
    "log_configuration_loaded",
    "log_configuration_saved",
    "log_error_with_context",
    "log_health_check",
    "log_key_event",
    "log_request_end",
    "log_request_start",
    "log_strategy_change",
    "setup_logging",
    # Time utilities
    "Timer",
    "add_seconds",
    "datetime_to_timestamp",
    "format_datetime",
    "format_duration",
    "format_relative_time",
    "get_current_datetime",
    "get_current_timestamp",
    "is_expired",
    "parse_datetime",
    "sleep_until",
    "subtract_seconds",
    "time_since",
    "time_until",
    "timestamp_to_datetime",
    # Validation utilities
    "sanitize_string",
    "validate_anthropic_request",
    "validate_content_type",
    "validate_http_method",
    "validate_json_structure",
    "validate_model_id",
    "validate_port_number",
    "validate_rate_limit",
    "validate_timeout_value",
    "validate_url",
    "validate_weight_value",
]
