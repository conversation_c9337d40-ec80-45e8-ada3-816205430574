# AMP 0.3 Environment Configuration Example
# Copy this file to .env and configure your actual values

# ==========================================
# Server Configuration
# ==========================================
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# ==========================================
# API Configuration
# ==========================================
TARGET_API_URL=https://ampcode.com/api/provider/anthropic/v1/messages
ANTHROPIC_VERSION=2023-06-01

# ==========================================
# File Paths
# ==========================================
KEYS_CONFIG_FILE=keys.json
MODELS_CONFIG_FILE=models.json
LOG_FILE=logs/amp.log

# ==========================================
# Retry & Timeout Settings
# ==========================================
MAX_RETRIES=3
REQUEST_TIMEOUT=120
CONNECT_TIMEOUT=10
RETRY_DELAY=1.0

# ==========================================
# Health Monitoring
# ==========================================
HEALTH_CHECK_INTERVAL=300
FAILURE_THRESHOLD=3
RECOVERY_TIME=300

# ==========================================
# Load Balancing
# ==========================================
DEFAULT_LOAD_BALANCER_STRATEGY=round_robin

# ==========================================
# Auto Registration Settings
# ==========================================
ENABLE_AUTO_REGISTRATION=true
MIN_ACTIVE_KEYS=5
MAX_REGISTRATION_ATTEMPTS=5
AUTO_REGISTRATION_HEADLESS=false

# ==========================================
# Security & Performance
# ==========================================
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# ==========================================
# Optional: Custom Configuration
# ==========================================
# Uncomment and configure as needed

# Custom API endpoint
# TARGET_API_URL=https://your-custom-endpoint.com/v1/messages

# Custom minimum keys threshold
# MIN_ACTIVE_KEYS=10

# Enable headless mode for auto registration (production)
# AUTO_REGISTRATION_HEADLESS=true

# Custom health check interval (seconds)
# HEALTH_CHECK_INTERVAL=600

# Custom failure threshold
# FAILURE_THRESHOLD=5

# Custom recovery time (seconds)
# RECOVERY_TIME=600
