"""
Data models for the Anthropic API Proxy.

This package contains all data models used throughout the application,
organized by functional domain.
"""

from .api import (
    AnthropicModel,
    ErrorResponse,
    Message,
    MessageContent,
    MessagesRequest,
    MessagesResponse,
    ModelsListResponse,
    ProxyRequest,
    ProxyResponse,
    StreamChunk,
    Usage,
)
from .base import (
    APIResponse,
    BaseConfigModel,
    HealthStatus,
    KeyStatus,
    LoadBalancerStrategy,
    PaginatedResponse,
    PaginationParams,
)
from .keys import (
    ClientKey,
    KeyStatistics,
    KeysConfig,
    UpstreamKey,
)

__all__ = [
    # Base models
    "APIResponse",
    "BaseConfigModel",
    "HealthStatus",
    "KeyStatus",
    "LoadBalancerStrategy",
    "PaginatedResponse",
    "PaginationParams",
    # Key models
    "ClientKey",
    "KeyStatistics",
    "KeysConfig",
    "UpstreamKey",
    # API models
    "AnthropicModel",
    "ErrorResponse",
    "Message",
    "MessageContent",
    "MessagesRequest",
    "MessagesResponse",
    "ModelsListResponse",
    "ProxyRequest",
    "ProxyResponse",
    "StreamChunk",
    "Usage",
]
