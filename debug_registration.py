#!/usr/bin/env python3
"""
Debug script to test registration page loading and element detection.
This script helps diagnose issues with the auto-registration process.
"""

import asyncio
import time
from playwright.async_api import async_playwright


async def debug_registration_page():
    """Debug the registration page to identify issues."""
    
    async with async_playwright() as p:
        # Launch browser with debugging options
        browser = await p.chromium.launch(
            headless=False,  # Set to True for headless mode
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--window-size=1920,1080'
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        
        try:
            print("🔍 Starting registration page debug...")
            
            # Navigate to registration page
            print("📄 Loading registration page...")
            response = await page.goto("https://auth.ampcode.com/sign-up", timeout=60000)
            print(f"✅ Page loaded with status: {response.status}")
            
            # Wait for page to fully load
            await asyncio.sleep(5)
            
            # Take screenshot
            screenshot_path = f"debug_page_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Get page title and URL
            title = await page.title()
            url = page.url
            print(f"📋 Page title: {title}")
            print(f"🔗 Current URL: {url}")
            
            # Check for common form selectors
            selectors_to_check = [
                'input[name="first_name"]',
                'input[name="firstName"]',
                'input[name="last_name"]',
                'input[name="lastName"]',
                'input[name="email"]',
                'input[type="email"]',
                'input[type="password"]',
                'button[type="submit"]',
                'form',
                'input',
                'button'
            ]
            
            print("\n🔍 Checking for form elements...")
            for selector in selectors_to_check:
                try:
                    elements = await page.locator(selector).count()
                    if elements > 0:
                        print(f"✅ Found {elements} element(s) with selector: {selector}")
                        
                        # Get element details
                        element = page.locator(selector).first
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        print(f"   - Visible: {is_visible}, Enabled: {is_enabled}")
                        
                        # Try to get attributes
                        try:
                            placeholder = await element.get_attribute('placeholder')
                            if placeholder:
                                print(f"   - Placeholder: {placeholder}")
                        except:
                            pass
                            
                        try:
                            element_id = await element.get_attribute('id')
                            if element_id:
                                print(f"   - ID: {element_id}")
                        except:
                            pass
                            
                        try:
                            class_name = await element.get_attribute('class')
                            if class_name:
                                print(f"   - Class: {class_name}")
                        except:
                            pass
                    else:
                        print(f"❌ No elements found with selector: {selector}")
                except Exception as e:
                    print(f"❌ Error checking selector {selector}: {e}")
            
            # Get page content for analysis
            content = await page.content()
            content_path = f"debug_content_{int(time.time())}.html"
            with open(content_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📄 Page content saved: {content_path}")
            
            # Check for any error messages or redirects
            print("\n🔍 Checking for error indicators...")
            error_selectors = [
                '.error',
                '.alert',
                '.warning',
                '[class*="error"]',
                '[class*="alert"]',
                '[id*="error"]'
            ]
            
            for selector in error_selectors:
                try:
                    elements = await page.locator(selector).count()
                    if elements > 0:
                        text = await page.locator(selector).first.text_content()
                        print(f"⚠️  Found error element: {selector} - {text}")
                except:
                    pass
            
            # Wait a bit more to see if anything loads dynamically
            print("\n⏳ Waiting for dynamic content...")
            await asyncio.sleep(10)
            
            # Take another screenshot
            screenshot_path2 = f"debug_page_after_wait_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path2, full_page=True)
            print(f"📸 Second screenshot saved: {screenshot_path2}")
            
            print("\n✅ Debug completed successfully!")
            
        except Exception as e:
            print(f"❌ Debug failed: {e}")
            
            # Take error screenshot
            try:
                error_screenshot = f"debug_error_{int(time.time())}.png"
                await page.screenshot(path=error_screenshot)
                print(f"📸 Error screenshot saved: {error_screenshot}")
            except:
                pass
                
        finally:
            await browser.close()


if __name__ == "__main__":
    print("🚀 Starting registration page debug...")
    asyncio.run(debug_registration_page())
    print("🏁 Debug completed!")
