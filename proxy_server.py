# proxy_server.py (版本 4.7.0 - 严格密钥管理版本)

import os
import json
import time
import random
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.responses import Response, JSONResponse, StreamingResponse
import httpx
from pydantic import BaseModel
import logging
from contextlib import asynccontextmanager

# --- 配置日志 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- 数据模型 ---
class UpstreamKey(BaseModel):
    key: str
    weight: int = 1
    enabled: bool = True
    failed_count: int = 0
    last_failed_time: Optional[datetime] = None
    total_requests: int = 0
    recovery_attempted: bool = False  # 新增：是否已经尝试过恢复
    permanently_disabled: bool = False  # 新增：是否永久禁用

class ClientKey(BaseModel):
    key: str
    enabled: bool = True
    description: str = ""

class KeysConfig(BaseModel):
    upstream_keys: List[UpstreamKey]
    client_keys: List[ClientKey]

# --- 负载均衡策略 ---
class LoadBalancerStrategy:
    @staticmethod
    def round_robin(keys: List[UpstreamKey], current_index: int) -> Tuple[UpstreamKey, int]:
        """简单轮询（排除永久禁用的密钥）"""
        active_keys = [k for k in keys if k.enabled and not k.permanently_disabled]
        if not active_keys:
            raise ValueError("没有可用的上游密钥")
        next_index = (current_index + 1) % len(active_keys)
        return active_keys[current_index], next_index

    @staticmethod
    def weighted_round_robin(keys: List[UpstreamKey], current_index: int) -> Tuple[UpstreamKey, int]:
        """加权轮询（排除永久禁用的密钥）"""
        active_keys = [k for k in keys if k.enabled and not k.permanently_disabled]
        if not active_keys:
            raise ValueError("没有可用的上游密钥")
        
        # 创建权重列表
        weighted_keys = []
        for key in active_keys:
            weighted_keys.extend([key] * key.weight)
        
        next_index = (current_index + 1) % len(weighted_keys)
        return weighted_keys[current_index], next_index

    @staticmethod
    def random(keys: List[UpstreamKey]) -> UpstreamKey:
        """随机选择（排除永久禁用的密钥）"""
        active_keys = [k for k in keys if k.enabled and not k.permanently_disabled]
        if not active_keys:
            raise ValueError("没有可用的上游密钥")
        return random.choice(active_keys)

    @staticmethod
    def least_used(keys: List[UpstreamKey]) -> UpstreamKey:
        """最少使用（排除永久禁用的密钥）"""
        active_keys = [k for k in keys if k.enabled and not k.permanently_disabled]
        if not active_keys:
            raise ValueError("没有可用的上游密钥")
        return min(active_keys, key=lambda k: k.total_requests)

# --- 密钥管理器 ---
class KeyManager:
    def __init__(self, config_file: str = "keys.json"):
        self.config_file = config_file
        self.config: KeysConfig = self.load_config()
        self.current_index = 0
        self.strategy = "round_robin"  # 默认策略
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 1  # 重试延迟（秒）
        self.health_check_interval = 300  # 健康检查间隔（秒）
        self.failure_threshold = 3  # 失败阈值
        self.recovery_time = 300  # 恢复时间（秒）
        
        # 加载模型列表
        self.available_models = self.load_models()

    def load_config(self) -> KeysConfig:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return KeysConfig(**data)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise

    def load_models(self) -> List[Dict[str, Any]]:
        """加载模型列表"""
        models_file = "models.json"
        try:
            with open(models_file, 'r', encoding='utf-8') as f:
                models = json.load(f)
                logger.info(f"成功加载 {len(models)} 个模型")
                return models
        except FileNotFoundError:
            logger.warning(f"模型配置文件 '{models_file}' 未找到，将返回空列表")
            return []
        except Exception as e:
            logger.error(f"加载模型配置失败: {e}")
            return []

    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.dict(), f, indent=4, default=str)
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise

    def get_next_key(self) -> UpstreamKey:
        """根据当前策略获取下一个密钥（排除永久禁用的密钥）"""
        # 过滤掉永久禁用的密钥
        available_keys = [k for k in self.config.upstream_keys if not k.permanently_disabled]
        
        if not available_keys:
            raise ValueError("没有可用的上游密钥（所有密钥都已永久禁用）")
        
        if self.strategy == "round_robin":
            key, self.current_index = LoadBalancerStrategy.round_robin(
                available_keys, self.current_index
            )
        elif self.strategy == "weighted_round_robin":
            key, self.current_index = LoadBalancerStrategy.weighted_round_robin(
                available_keys, self.current_index
            )
        elif self.strategy == "random":
            key = LoadBalancerStrategy.random(available_keys)
        elif self.strategy == "least_used":
            key = LoadBalancerStrategy.least_used(available_keys)
        else:
            raise ValueError(f"未知的负载均衡策略: {self.strategy}")

        key.total_requests += 1
        self.save_config()
        return key

    def mark_key_failed(self, key: UpstreamKey):
        """标记密钥失败"""
        # 如果已经永久禁用，不再处理
        if key.permanently_disabled:
            return
            
        key.failed_count += 1
        key.last_failed_time = datetime.now()
        
        if key.failed_count >= self.failure_threshold:  # 默认3次
            if key.recovery_attempted:
                # 如果已经尝试过恢复，这次失败就永久禁用
                key.permanently_disabled = True
                key.enabled = False
                logger.error(f"密钥永久禁用（恢复后仍失效）: {self.mask_key(key.key)}")
            else:
                # 第一次达到失败阈值，临时禁用
                key.enabled = False
                logger.warning(f"密钥临时禁用（将在{self.recovery_time}秒后尝试恢复）: {self.mask_key(key.key)}")
        
        self.save_config()

    def check_key_health(self, key: UpstreamKey) -> bool:
        """检查密钥健康状态"""
        # 永久禁用的密钥不再检查
        if key.permanently_disabled:
            return False
            
        if not key.enabled and key.last_failed_time and not key.recovery_attempted:
            # 检查是否已经过了恢复时间
            if datetime.now() - key.last_failed_time > timedelta(seconds=self.recovery_time):
                key.enabled = True
                key.failed_count = 0  # 重置失败计数，给它一个全新的机会
                key.recovery_attempted = True  # 标记已经尝试过恢复
                key.last_failed_time = None
                self.save_config()
                logger.info(f"密钥已恢复（最后一次机会）: {self.mask_key(key.key)}")
        
        return key.enabled

    async def health_check_all(self):
        """检查所有密钥的健康状态"""
        while True:
            logger.info("开始健康检查...")
            for key in self.config.upstream_keys:
                self.check_key_health(key)
            await asyncio.sleep(self.health_check_interval)

    def verify_client_key(self, key: str) -> bool:
        """验证客户端密钥"""
        return any(k.key == key and k.enabled for k in self.config.client_keys)

    def get_key_status_summary(self) -> Dict[str, int]:
        """获取密钥状态摘要"""
        summary = {
            "total": len(self.config.upstream_keys),
            "active": 0,
            "temporarily_disabled": 0,
            "permanently_disabled": 0
        }
        
        for key in self.config.upstream_keys:
            if key.permanently_disabled:
                summary["permanently_disabled"] += 1
            elif key.enabled:
                summary["active"] += 1
            else:
                summary["temporarily_disabled"] += 1
                
        return summary

    @staticmethod
    def mask_key(key: str) -> str:
        """遮蔽密钥"""
        if len(key) <= 4:
            return "****"
        return f"...{key[-4:]}"

# --- 全局变量 ---
client: httpx.AsyncClient = None
key_manager: KeyManager = None

# --- 应用生命周期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    global client, key_manager
    timeout = httpx.Timeout(120.0, connect=10.0)
    client = httpx.AsyncClient(timeout=timeout)
    key_manager = KeyManager()
    
    # 启动健康检查任务
    health_check_task = asyncio.create_task(key_manager.health_check_all())
    
    logger.info("HTTP客户端和密钥管理器已初始化")
    
    yield
    
    # 关闭时清理
    health_check_task.cancel()
    if client:
        await client.aclose()
    logger.info("资源已清理")

# --- 初始化 FastAPI ---
app = FastAPI(
    title="Anthropic API Proxy (Strict Key Management)",
    description="支持严格密钥管理、多种负载均衡策略、自动重试和健康检查的代理服务",
    version="4.7.0",
    lifespan=lifespan
)

TARGET_MESSAGES_URL = "https://ampcode.com/api/provider/anthropic/v1/messages"

# --- 工具函数 ---
async def verify_client_api_key(request: Request) -> str:
    """验证客户端API密钥"""
    api_key = None
    
    # 从各种可能的位置提取密钥
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.startswith("Bearer "):
        api_key = auth_header[7:]
    else:
        api_key = request.headers.get("x-api-key") or request.headers.get("anthropic-api-key")

    if not api_key:
        raise HTTPException(
            status_code=401,
            detail="缺少API密钥"
        )

    if not key_manager.verify_client_key(api_key):
        raise HTTPException(
            status_code=401,
            detail="无效的API密钥"
        )

    return api_key

async def stream_response_generator(request_data: Dict[str, Any], headers: Dict[str, str]):
    """流式响应生成器（带重试）"""
    retry_count = 0
    while retry_count <= key_manager.max_retries:
        try:
            current_key = key_manager.get_next_key()
            headers['x-api-key'] = current_key.key
            
            async with client.stream(
                "POST",
                TARGET_MESSAGES_URL,
                json=request_data,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_content = await response.aread()
                    key_manager.mark_key_failed(current_key)
                    if retry_count < key_manager.max_retries:
                        retry_count += 1
                        await asyncio.sleep(key_manager.retry_delay)
                        continue
                    
                    error_data = {
                        "error": f"上游服务器错误: {response.status_code}",
                        "detail": error_content.decode() if error_content else "未知错误"
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"
                    return
                
                async for chunk in response.aiter_bytes():
                    if chunk:
                        yield chunk
                return
                    
        except Exception as e:
            key_manager.mark_key_failed(current_key)
            if retry_count < key_manager.max_retries:
                retry_count += 1
                await asyncio.sleep(key_manager.retry_delay)
                continue
            
            error_data = {"error": f"请求失败: {str(e)}"}
            yield f"data: {json.dumps(error_data)}\n\n"
            return

async def forward_request(request_data: Dict[str, Any], headers: Dict[str, str]) -> Response:
    """转发请求（带重试）"""
    is_streaming = request_data.get("stream", False)
    
    if is_streaming:
        return StreamingResponse(
            stream_response_generator(request_data, headers),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )
    
    # 非流式请求处理
    retry_count = 0
    while retry_count <= key_manager.max_retries:
        try:
            current_key = key_manager.get_next_key()
            headers['x-api-key'] = current_key.key
            
            response = await client.post(
                TARGET_MESSAGES_URL,
                json=request_data,
                headers=headers
            )
            
            if response.status_code != 200:
                key_manager.mark_key_failed(current_key)
                if retry_count < key_manager.max_retries:
                    retry_count += 1
                    await asyncio.sleep(key_manager.retry_delay)
                    continue
            
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
        except Exception as e:
            key_manager.mark_key_failed(current_key)
            if retry_count < key_manager.max_retries:
                retry_count += 1
                await asyncio.sleep(key_manager.retry_delay)
                continue
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

# --- API 端点 ---
@app.get("/")
def read_root():
    """根路径健康检查"""
    return {
        "status": "ok",
        "version": app.version,
        "features": ["strict_key_management", "load_balancing", "auto_retry", "health_check"],
        "config": {
            "strategy": key_manager.strategy,
            "max_retries": key_manager.max_retries,
            "health_check_interval": key_manager.health_check_interval,
            "upstream_keys": len(key_manager.config.upstream_keys),
            "client_keys": len(key_manager.config.client_keys),
            "models_loaded": len(key_manager.available_models)
        },
        "key_summary": key_manager.get_key_status_summary()
    }

@app.get("/health")
async def health_check(client_key: str = Depends(verify_client_api_key)):
    """详细的健康状态"""
    return {
        "status": "healthy",
        "version": app.version,
        "load_balancer": {
            "strategy": key_manager.strategy,
            "current_index": key_manager.current_index
        },
        "key_summary": key_manager.get_key_status_summary(),
        "upstream_keys": [
            {
                "key": key_manager.mask_key(k.key),
                "enabled": k.enabled,
                "permanently_disabled": k.permanently_disabled,
                "recovery_attempted": k.recovery_attempted,
                "requests": k.total_requests,
                "failures": k.failed_count,
                "last_failed": k.last_failed_time,
                "status": (
                    "永久禁用" if k.permanently_disabled 
                    else "正常" if k.enabled 
                    else "临时禁用"
                )
            }
            for k in key_manager.config.upstream_keys
        ],
        "config": {
            "max_retries": key_manager.max_retries,
            "retry_delay": key_manager.retry_delay,
            "health_check_interval": key_manager.health_check_interval,
            "failure_threshold": key_manager.failure_threshold,
            "recovery_time": key_manager.recovery_time
        }
    }

@app.get("/v1/models")
async def list_models(client_key: str = Depends(verify_client_api_key)):
    """返回可用的模型列表"""
    logger.info(f"收到 /v1/models 请求，客户端密钥: {key_manager.mask_key(client_key)}")
    return JSONResponse(content={
        "object": "list",
        "data": key_manager.available_models
    })

@app.post("/v1/messages")
async def proxy_messages_request(
    request: Request,
    client_key: str = Depends(verify_client_api_key)
):
    """处理消息请求"""
    try:
        request_data = await request.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail="无效的JSON请求体")

    is_streaming = request_data.get("stream", False)
    logger.info(f"收到 /v1/messages 请求，客户端密钥: {key_manager.mask_key(client_key)}，流式: {is_streaming}")

    headers = {
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json',
    }

    if is_streaming:
        headers['Accept'] = 'text/event-stream'

    return await forward_request(request_data, headers)

@app.post("/admin/strategy")
async def set_strategy(
    strategy: str,
    client_key: str = Depends(verify_client_api_key)
):
    """设置负载均衡策略"""
    valid_strategies = ["round_robin", "weighted_round_robin", "random", "least_used"]
    if strategy not in valid_strategies:
        raise HTTPException(
            status_code=400,
            detail=f"无效的策略。可用策略: {', '.join(valid_strategies)}"
        )
    
    key_manager.strategy = strategy
    logger.info(f"负载均衡策略已更改为: {strategy}")
    return {"message": f"已设置负载均衡策略为: {strategy}"}

@app.get("/admin/stats")
async def get_stats(client_key: str = Depends(verify_client_api_key)):
    """获取统计信息"""
    return {
        "load_balancer": {
            "strategy": key_manager.strategy,
            "current_index": key_manager.current_index
        },
        "key_summary": key_manager.get_key_status_summary(),
        "keys_stats": [
            {
                "key": key_manager.mask_key(k.key),
                "enabled": k.enabled,
                "permanently_disabled": k.permanently_disabled,
                "recovery_attempted": k.recovery_attempted,
                "weight": k.weight,
                "total_requests": k.total_requests,
                "failed_count": k.failed_count,
                "last_failed_time": k.last_failed_time,
                "status": (
                    "永久禁用" if k.permanently_disabled 
                    else "正常" if k.enabled 
                    else "临时禁用"
                )
            }
            for k in key_manager.config.upstream_keys
        ],
        "models_count": len(key_manager.available_models)
    }

@app.get("/admin/keys/status")
async def get_keys_status(client_key: str = Depends(verify_client_api_key)):
    """获取所有密钥的详细状态"""
    return {
        "summary": key_manager.get_key_status_summary(),
        "keys": [
            {
                "key_suffix": k.key[-4:],
                "status": (
                    "永久禁用" if k.permanently_disabled 
                    else "正常" if k.enabled 
                    else "临时禁用"
                ),
                "enabled": k.enabled,
                "permanently_disabled": k.permanently_disabled,
                "recovery_attempted": k.recovery_attempted,
                "weight": k.weight,
                "total_requests": k.total_requests,
                "failed_count": k.failed_count,
                "last_failed_time": k.last_failed_time
            }
            for k in key_manager.config.upstream_keys
        ]
    }

@app.post("/admin/keys/reset")
async def reset_key_status(
    key_suffix: str,  # 密钥的后4位
    client_key: str = Depends(verify_client_api_key)
):
    """重置指定密钥的状态（清除永久禁用标记）"""
    for key in key_manager.config.upstream_keys:
        if key.key.endswith(key_suffix):
            key.permanently_disabled = False
            key.recovery_attempted = False
            key.enabled = True
            key.failed_count = 0
            key.last_failed_time = None
            key_manager.save_config()
            
            logger.info(f"密钥状态已重置: {key_manager.mask_key(key.key)}")
            return {"message": f"密钥 ...{key_suffix} 状态已重置"}
    
    raise HTTPException(status_code=404, detail=f"未找到后缀为 {key_suffix} 的密钥")

@app.post("/admin/keys/{key_suffix}/disable")
async def disable_key(
    key_suffix: str,
    client_key: str = Depends(verify_client_api_key)
):
    """手动禁用密钥"""
    for key in key_manager.config.upstream_keys:
        if key.key.endswith(key_suffix):
            key.enabled = False
            key_manager.save_config()
            logger.info(f"密钥已手动禁用: {key_manager.mask_key(key.key)}")
            return {"message": f"密钥 ...{key_suffix} 已禁用"}
    
    raise HTTPException(status_code=404, detail=f"未找到后缀为 {key_suffix} 的密钥")

@app.post("/admin/keys/{key_suffix}/enable")
async def enable_key(
    key_suffix: str,
    client_key: str = Depends(verify_client_api_key)
):
    """手动启用密钥"""
    for key in key_manager.config.upstream_keys:
        if key.key.endswith(key_suffix):
            if key.permanently_disabled:
                return {"message": f"密钥 ...{key_suffix} 已永久禁用，请使用重置功能"}
            key.enabled = True
            key_manager.save_config()
            logger.info(f"密钥已手动启用: {key_manager.mask_key(key.key)}")
            return {"message": f"密钥 ...{key_suffix} 已启用"}
    
    raise HTTPException(status_code=404, detail=f"未找到后缀为 {key_suffix} 的密钥")

# --- 错误处理 ---
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTP异常 {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误"}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
