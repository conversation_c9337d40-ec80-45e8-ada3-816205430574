#!/usr/bin/env python3
"""
Standalone runner for the Anthropic API Proxy.

This script provides a simple way to run the proxy server
with command-line arguments and environment variable support.
"""

import argparse
import os
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.main import main


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Anthropic API Proxy Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Environment Variables:
  HOST                    Server host (default: 0.0.0.0)
  PORT                    Server port (default: 8000)
  DEBUG                   Enable debug mode (default: False)
  RELOAD                  Enable auto-reload (default: False)
  LOG_LEVEL              Logging level (default: INFO)
  KEYS_CONFIG_FILE       Path to keys config file (default: keys.json)
  MODELS_CONFIG_FILE     Path to models config file (default: models.json)
  TARGET_API_URL         Target Anthropic API URL
  MAX_RETRIES            Maximum retry attempts (default: 3)
  HEALTH_CHECK_INTERVAL  Health check interval in seconds (default: 300)

Examples:
  python run.py
  python run.py --host 127.0.0.1 --port 8080 --debug
  python run.py --reload --log-level DEBUG
        """
    )
    
    parser.add_argument(
        "--host",
        default=os.getenv("HOST", "0.0.0.0"),
        help="Host to bind to (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=int(os.getenv("PORT", "8000")),
        help="Port to bind to (default: 8000)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        default=os.getenv("DEBUG", "").lower() in ("true", "1", "yes"),
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        default=os.getenv("RELOAD", "").lower() in ("true", "1", "yes"),
        help="Enable auto-reload for development"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default=os.getenv("LOG_LEVEL", "INFO"),
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--keys-config",
        default=os.getenv("KEYS_CONFIG_FILE", "keys.json"),
        help="Path to keys configuration file (default: keys.json)"
    )
    
    parser.add_argument(
        "--models-config",
        default=os.getenv("MODELS_CONFIG_FILE", "models.json"),
        help="Path to models configuration file (default: models.json)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Anthropic API Proxy v4.7.0"
    )
    
    return parser.parse_args()


def setup_environment(args):
    """Set up environment variables from command line arguments."""
    os.environ["HOST"] = args.host
    os.environ["PORT"] = str(args.port)
    os.environ["DEBUG"] = str(args.debug)
    os.environ["RELOAD"] = str(args.reload)
    os.environ["LOG_LEVEL"] = args.log_level
    os.environ["KEYS_CONFIG_FILE"] = args.keys_config
    os.environ["MODELS_CONFIG_FILE"] = args.models_config


def check_config_files(args):
    """Check if configuration files exist and are readable."""
    config_files = [
        (args.keys_config, "keys configuration"),
        (args.models_config, "models configuration")
    ]
    
    for file_path, description in config_files:
        path = Path(file_path)
        if not path.exists():
            print(f"Warning: {description} file not found: {file_path}")
            print(f"The application will create a default configuration.")
        elif not path.is_file():
            print(f"Error: {description} path is not a file: {file_path}")
            sys.exit(1)
        elif not os.access(path, os.R_OK):
            print(f"Error: {description} file is not readable: {file_path}")
            sys.exit(1)


def main_cli():
    """Main CLI entry point."""
    try:
        args = parse_arguments()
        
        # Set up environment from arguments
        setup_environment(args)
        
        # Check configuration files
        check_config_files(args)
        
        # Print startup information
        print(f"Starting Anthropic API Proxy v4.7.0")
        print(f"Host: {args.host}")
        print(f"Port: {args.port}")
        print(f"Debug: {args.debug}")
        print(f"Reload: {args.reload}")
        print(f"Log Level: {args.log_level}")
        print(f"Keys Config: {args.keys_config}")
        print(f"Models Config: {args.models_config}")
        print("-" * 50)
        
        # Start the application
        main()
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main_cli()
