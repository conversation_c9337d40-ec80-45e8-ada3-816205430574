"""
Tests for utility functions.

This module contains unit tests for utility functions including
key utilities, validation, time utilities, and logging utilities.
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import MagicMock, patch

from src.utils import (
    Timer,
    compare_key_suffixes,
    extract_key_suffix,
    format_duration,
    format_relative_time,
    get_current_datetime,
    get_current_timestamp,
    is_expired,
    mask_key,
    normalize_key,
    sanitize_key_for_logging,
    sanitize_string,
    time_since,
    time_until,
    validate_anthropic_request,
    validate_key_format,
    validate_model_id,
    validate_port_number,
    validate_timeout_value,
    validate_url,
    validate_weight_value,
)


class TestKeyUtilities:
    """Test cases for key utility functions."""
    
    def test_mask_key(self):
        """Test key masking functionality."""
        # Normal key
        assert mask_key("sk-1234567890abcdef") == "...cdef"
        
        # Short key
        assert mask_key("short") == "*****"
        
        # Empty key
        assert mask_key("") == "****"
        
        # Custom parameters
        assert mask_key("sk-1234567890abcdef", visible_chars=6) == "...90abcdef"
        assert mask_key("test", mask_char="#") == "####"
    
    def test_extract_key_suffix(self):
        """Test extracting key suffix."""
        assert extract_key_suffix("sk-1234567890abcdef") == "cdef"
        assert extract_key_suffix("short", 2) == "rt"
        assert extract_key_suffix("ab", 5) == "ab"
        assert extract_key_suffix("") == ""
    
    def test_compare_key_suffixes(self):
        """Test comparing key suffixes."""
        assert compare_key_suffixes("sk-1234567890abcdef", "different-prefix-cdef") is True
        assert compare_key_suffixes("sk-1234567890abcdef", "sk-1234567890xyz") is False
        assert compare_key_suffixes("", "test") is False
        assert compare_key_suffixes("test", "") is False
    
    def test_validate_key_format(self):
        """Test key format validation."""
        # Valid keys
        assert validate_key_format("sk-1234567890abcdef") is True
        assert validate_key_format("valid_key_123") is True
        
        # Invalid keys
        assert validate_key_format("") is False
        assert validate_key_format("short") is False  # Too short
        assert validate_key_format("  key_with_spaces  ") is False
        assert validate_key_format("key\nwith\nnewlines") is False
        assert validate_key_format("a" * 201) is False  # Too long
    
    def test_normalize_key(self):
        """Test key normalization."""
        assert normalize_key("  sk-1234567890abcdef  ") == "sk-1234567890abcdef"
        
        with pytest.raises(ValueError):
            normalize_key("")
        
        with pytest.raises(ValueError):
            normalize_key("   ")
    
    def test_sanitize_key_for_logging(self):
        """Test key sanitization for logging."""
        assert sanitize_key_for_logging("sk-1234567890abcdef") == "KEY(...cdef)"
        assert sanitize_key_for_logging("") == "KEY(empty)"
        assert sanitize_key_for_logging("short") == "KEY(****)"


class TestValidationUtilities:
    """Test cases for validation utility functions."""
    
    def test_validate_url(self):
        """Test URL validation."""
        # Valid URLs
        assert validate_url("https://api.anthropic.com/v1/messages") is True
        assert validate_url("http://localhost:8000") is True
        
        # Invalid URLs
        assert validate_url("not-a-url") is False
        assert validate_url("") is False
        assert validate_url("ftp://example.com") is True  # Valid but different scheme
    
    def test_validate_model_id(self):
        """Test model ID validation."""
        # Valid model IDs
        assert validate_model_id("claude-3-sonnet-20240229") is True
        assert validate_model_id("claude-3-haiku") is True
        assert validate_model_id("model_123") is True
        
        # Invalid model IDs
        assert validate_model_id("") is False
        assert validate_model_id("invalid model id") is False  # Contains space
        assert validate_model_id("ab") is False  # Too short
        assert validate_model_id("a" * 101) is False  # Too long
    
    def test_validate_port_number(self):
        """Test port number validation."""
        # Valid ports
        assert validate_port_number(8000) is True
        assert validate_port_number("8080") is True
        assert validate_port_number(1) is True
        assert validate_port_number(65535) is True
        
        # Invalid ports
        assert validate_port_number(0) is False
        assert validate_port_number(70000) is False
        assert validate_port_number("invalid") is False
        assert validate_port_number(-1) is False
    
    def test_validate_timeout_value(self):
        """Test timeout value validation."""
        # Valid timeouts
        assert validate_timeout_value(30.0) is True
        assert validate_timeout_value("60") is True
        assert validate_timeout_value(1) is True
        
        # Invalid timeouts
        assert validate_timeout_value(-1) is False
        assert validate_timeout_value(0) is False
        assert validate_timeout_value(3601) is False  # Too large
        assert validate_timeout_value("invalid") is False
    
    def test_validate_weight_value(self):
        """Test weight value validation."""
        # Valid weights
        assert validate_weight_value(1) is True
        assert validate_weight_value("5") is True
        assert validate_weight_value(100) is True
        
        # Invalid weights
        assert validate_weight_value(0) is False
        assert validate_weight_value(101) is False
        assert validate_weight_value("invalid") is False
        assert validate_weight_value(-1) is False
    
    def test_validate_anthropic_request(self):
        """Test Anthropic request validation."""
        # Valid request
        valid_request = {
            "model": "claude-3-sonnet",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 100
        }
        is_valid, error = validate_anthropic_request(valid_request)
        assert is_valid is True
        assert error is None
        
        # Missing required field
        invalid_request = {
            "model": "claude-3-sonnet",
            "max_tokens": 100
        }
        is_valid, error = validate_anthropic_request(invalid_request)
        assert is_valid is False
        assert "messages" in error
        
        # Invalid message role
        invalid_request = {
            "model": "claude-3-sonnet",
            "messages": [{"role": "invalid", "content": "Hello"}],
            "max_tokens": 100
        }
        is_valid, error = validate_anthropic_request(invalid_request)
        assert is_valid is False
        assert "invalid role" in error
    
    def test_sanitize_string(self):
        """Test string sanitization."""
        # Normal string
        assert sanitize_string("Hello World!") == "Hello World!"
        
        # String with control characters
        assert sanitize_string("Test\x00\x01") == "Test"
        
        # Long string
        long_string = "a" * 1500
        result = sanitize_string(long_string, max_length=1000)
        assert len(result) == 1000
        
        # Non-string input
        assert sanitize_string(123) == ""


class TestTimeUtilities:
    """Test cases for time utility functions."""
    
    def test_get_current_timestamp(self):
        """Test getting current timestamp."""
        timestamp = get_current_timestamp()
        assert isinstance(timestamp, float)
        assert timestamp > 0
    
    def test_get_current_datetime(self):
        """Test getting current datetime."""
        dt = get_current_datetime()
        assert isinstance(dt, datetime)
        assert dt.tzinfo is not None
    
    def test_format_duration(self):
        """Test duration formatting."""
        assert format_duration(3661) == "1h 1m 1s"
        assert format_duration(90) == "1m 30s"
        assert format_duration(45) == "45s"
        assert format_duration(0) == "0s"
        assert format_duration(-10) == "0s"
    
    def test_format_relative_time(self):
        """Test relative time formatting."""
        now = get_current_datetime()
        
        # Past time
        past = now - timedelta(hours=2)
        result = format_relative_time(past)
        assert "ago" in result
        assert "hour" in result
        
        # Future time
        future = now + timedelta(minutes=30)
        result = format_relative_time(future)
        assert "from now" in result
        assert "minute" in result
    
    def test_time_since(self):
        """Test calculating time since."""
        past = get_current_datetime() - timedelta(hours=1)
        elapsed = time_since(past)
        assert isinstance(elapsed, timedelta)
        assert elapsed.total_seconds() > 3500  # About 1 hour
    
    def test_time_until(self):
        """Test calculating time until."""
        future = get_current_datetime() + timedelta(hours=1)
        remaining = time_until(future)
        assert isinstance(remaining, timedelta)
        assert remaining.total_seconds() > 3500  # About 1 hour
    
    def test_is_expired(self):
        """Test expiry checking."""
        # Expired
        past = get_current_datetime() - timedelta(hours=2)
        assert is_expired(past, 3600) is True  # 1 hour expiry
        
        # Not expired
        recent = get_current_datetime() - timedelta(minutes=30)
        assert is_expired(recent, 3600) is False  # 1 hour expiry


class TestTimer:
    """Test cases for Timer class."""
    
    def test_timer_basic_usage(self):
        """Test basic timer usage."""
        timer = Timer()
        
        # Timer not started
        with pytest.raises(ValueError):
            timer.elapsed()
        
        # Start timer
        timer.start()
        assert timer.is_running is True
        
        # Get elapsed time
        elapsed = timer.elapsed()
        assert elapsed >= 0
        
        # Stop timer
        final_elapsed = timer.stop()
        assert final_elapsed >= elapsed
        assert timer.is_running is False
    
    def test_timer_reset(self):
        """Test timer reset."""
        timer = Timer()
        timer.start()
        timer.stop()
        
        timer.reset()
        assert timer.is_running is False
        
        with pytest.raises(ValueError):
            timer.elapsed()
    
    @patch('time.sleep')
    def test_timer_with_delay(self, mock_sleep):
        """Test timer with simulated delay."""
        timer = Timer()
        
        with patch('src.utils.time.get_current_timestamp') as mock_time:
            # Mock time progression
            mock_time.side_effect = [1000.0, 1001.5, 1002.0]
            
            timer.start()
            elapsed = timer.elapsed()
            final_elapsed = timer.stop()
            
            assert elapsed == 1.5
            assert final_elapsed == 2.0
