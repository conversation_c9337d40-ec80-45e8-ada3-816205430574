"""
API routers module.

This package contains all FastAPI routers organized by functional domain,
including core API endpoints, administrative functions, and health checks.
"""

from . import admin, core, health
from .dependencies import (
    RequestContext,
    get_anthropic_headers,
    get_optional_client_key,
    get_request_context,
    get_request_info,
    rate_limiter,
    validate_json_request,
    verify_client_api_key,
)

__all__ = [
    # Router modules
    "admin",
    "core",
    "health",
    # Dependencies
    "RequestContext",
    "get_anthropic_headers",
    "get_optional_client_key",
    "get_request_context",
    "get_request_info",
    "rate_limiter",
    "validate_json_request",
    "verify_client_api_key",
]
