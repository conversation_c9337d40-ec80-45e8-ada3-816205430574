"""
Core API routes for Anthropic API proxy.

This module contains the main API endpoints that proxy requests
to the Anthropic API, including messages and models endpoints.
"""

import asyncio
import json
import logging
from typing import Any, Dict

import httpx
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse, Response, StreamingResponse

from ..config import load_models_config, settings
from ..key_manager import key_manager
from ..models import MessagesRequest, ModelsListResponse
from ..utils import (
    Timer,
    get_logger,
    log_error_with_context,
    log_request_end,
    log_request_start,
    sanitize_key_for_logging,
    validate_anthropic_request,
)
from .dependencies import (
    RequestContext,
    get_anthropic_headers,
    get_request_context,
    verify_client_api_key,
)

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/v1", tags=["core"])

# Global HTTP client (will be initialized in lifespan)
http_client: httpx.AsyncClient = None


def set_http_client(client: httpx.AsyncClient) -> None:
    """Set the global HTTP client."""
    global http_client
    http_client = client


async def stream_response_generator(
    request_data: Dict[str, Any],
    headers: Dict[str, str],
    context: RequestContext
) -> Any:
    """
    Generate streaming response with retry logic.
    
    Args:
        request_data: Request data to send
        headers: Headers for the request
        context: Request context
        
    Yields:
        Response chunks
    """
    retry_count = 0
    max_retries = settings.max_retries
    
    while retry_count <= max_retries:
        try:
            # Get upstream key
            upstream_key = key_manager.get_next_upstream_key()
            context.set_upstream_key(upstream_key.key)
            headers['x-api-key'] = upstream_key.key
            
            logger.debug(
                f"Streaming request attempt {retry_count + 1} with key "
                f"{sanitize_key_for_logging(upstream_key.key)}"
            )
            
            async with http_client.stream(
                "POST",
                settings.target_api_url,
                json=request_data,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_content = await response.aread()
                    key_manager.mark_key_failed(upstream_key)
                    
                    if retry_count < max_retries:
                        retry_count += 1
                        await asyncio.sleep(settings.retry_delay)
                        continue
                    
                    # Final failure
                    error_data = {
                        "error": f"Upstream server error: {response.status_code}",
                        "detail": error_content.decode() if error_content else "Unknown error"
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"
                    return
                
                # Success - stream the response
                async for chunk in response.aiter_bytes():
                    if chunk:
                        yield chunk
                return
                
        except Exception as e:
            key_manager.mark_key_failed(upstream_key)
            
            if retry_count < max_retries:
                retry_count += 1
                logger.warning(
                    f"Streaming request failed (attempt {retry_count}): {e}"
                )
                await asyncio.sleep(settings.retry_delay)
                continue
            
            # Final failure
            log_error_with_context(logger, e, "Streaming request failed")
            error_data = {"error": f"Request failed: {str(e)}"}
            yield f"data: {json.dumps(error_data)}\n\n"
            return


async def forward_request(
    request_data: Dict[str, Any],
    headers: Dict[str, str],
    context: RequestContext
) -> Response:
    """
    Forward a request to the upstream API with retry logic.
    
    Args:
        request_data: Request data to send
        headers: Headers for the request
        context: Request context
        
    Returns:
        Response from upstream API
        
    Raises:
        HTTPException: If all retries fail
    """
    is_streaming = request_data.get("stream", False)
    context.set_streaming(is_streaming)
    
    if is_streaming:
        return StreamingResponse(
            stream_response_generator(request_data, headers, context),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )
    
    # Non-streaming request
    retry_count = 0
    max_retries = settings.max_retries
    
    while retry_count <= max_retries:
        try:
            # Get upstream key
            upstream_key = key_manager.get_next_upstream_key()
            context.set_upstream_key(upstream_key.key)
            headers['x-api-key'] = upstream_key.key
            
            logger.debug(
                f"Request attempt {retry_count + 1} with key "
                f"{sanitize_key_for_logging(upstream_key.key)}"
            )
            
            response = await http_client.post(
                settings.target_api_url,
                json=request_data,
                headers=headers
            )
            
            if response.status_code != 200:
                key_manager.mark_key_failed(upstream_key)
                
                if retry_count < max_retries:
                    retry_count += 1
                    await asyncio.sleep(settings.retry_delay)
                    continue
            
            # Success or final attempt
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
        except Exception as e:
            key_manager.mark_key_failed(upstream_key)
            
            if retry_count < max_retries:
                retry_count += 1
                logger.warning(f"Request failed (attempt {retry_count}): {e}")
                await asyncio.sleep(settings.retry_delay)
                continue
            
            # Final failure
            log_error_with_context(logger, e, "Request failed after all retries")
            raise HTTPException(
                status_code=500,
                detail=f"Request failed after {max_retries + 1} attempts: {str(e)}"
            )


@router.get("/models")
async def list_models(
    client_key: str = Depends(verify_client_api_key)
) -> JSONResponse:
    """
    List available models.
    
    This endpoint returns the list of available Anthropic models
    that can be used with the proxy.
    
    Args:
        client_key: Validated client API key
        
    Returns:
        JSON response with models list
    """
    timer = Timer()
    timer.start()
    
    log_request_start(logger, "GET", "/v1/models", client_key=client_key)
    
    try:
        # Load models from configuration
        models = load_models_config()
        
        response_data = ModelsListResponse(
            object="list",
            data=models
        )
        
        processing_time = timer.stop()
        log_request_end(logger, "GET", "/v1/models", 200, processing_time)
        
        return JSONResponse(content=response_data.dict())
        
    except Exception as e:
        processing_time = timer.elapsed()
        log_error_with_context(logger, e, "Failed to list models")
        log_request_end(logger, "GET", "/v1/models", 500, processing_time)
        
        raise HTTPException(
            status_code=500,
            detail="Failed to load models configuration"
        )


@router.post("/messages")
async def proxy_messages_request(
    request: Request,
    client_key: str = Depends(verify_client_api_key),
    headers: Dict[str, str] = Depends(get_anthropic_headers),
    context: RequestContext = Depends(get_request_context)
) -> Response:
    """
    Proxy messages requests to Anthropic API.
    
    This is the main endpoint that forwards chat completion requests
    to the Anthropic API with load balancing and retry logic.
    
    Args:
        request: FastAPI request object
        client_key: Validated client API key
        headers: Prepared headers for upstream request
        context: Request context
        
    Returns:
        Response from Anthropic API (streaming or non-streaming)
    """
    timer = Timer()
    timer.start()
    
    context.set_client_key(client_key)
    
    try:
        # Parse and validate request data
        request_data = await request.json()
        
        # Validate request structure
        is_valid, error_message = validate_anthropic_request(request_data)
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)
        
        is_streaming = request_data.get("stream", False)
        
        log_request_start(
            logger, "POST", "/v1/messages",
            client_key=client_key
        )
        
        logger.info(
            f"Processing messages request: model={request_data.get('model')}, "
            f"streaming={is_streaming}, client={sanitize_key_for_logging(client_key)}"
        )
        
        # Forward the request
        response = await forward_request(request_data, headers, context)
        
        if not is_streaming:
            processing_time = timer.stop()
            log_request_end(
                logger, "POST", "/v1/messages",
                response.status_code, processing_time,
                upstream_key=context.upstream_key
            )
        
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        processing_time = timer.elapsed()
        log_request_end(logger, "POST", "/v1/messages", 400, processing_time)
        raise
        
    except Exception as e:
        processing_time = timer.elapsed()
        log_error_with_context(logger, e, "Messages request failed")
        log_request_end(logger, "POST", "/v1/messages", 500, processing_time)
        
        raise HTTPException(
            status_code=500,
            detail="Internal server error processing messages request"
        )
