"""
Key management core functionality.

This module contains the main KeyManager class responsible for managing
upstream and client API keys, including selection, health monitoring,
and failure handling.
"""

import asyncio
import logging
from typing import List, Optional

from ..config import load_keys_config, save_keys_config, settings
from ..load_balancer import strategy_manager
from ..models import ClientKey, KeyStatistics, KeysConfig, UpstreamKey
from ..utils import get_current_datetime, get_logger, log_key_event, mask_key
from .health import HealthMonitor

logger = get_logger(__name__)


class KeyManager:
    """
    Main key management class.
    
    This class is responsible for:
    - Loading and saving key configurations
    - Managing upstream key selection using load balancing strategies
    - Handling key failures and recovery
    - Validating client keys
    - Providing key statistics and status information
    """
    
    def __init__(self):
        """Initialize the key manager."""
        self._config: Optional[KeysConfig] = None
        self._health_monitor: Optional[HealthMonitor] = None
        self._health_task: Optional[asyncio.Task] = None
        
        # Load initial configuration
        self.reload_config()
        
        # Initialize health monitor
        self._health_monitor = HealthMonitor(self)
    
    async def start(self) -> None:
        """Start the key manager and its background tasks."""
        if self._health_monitor and not self._health_task:
            self._health_task = asyncio.create_task(
                self._health_monitor.start_monitoring()
            )
            logger.info("Key manager started with health monitoring")
    
    async def stop(self) -> None:
        """Stop the key manager and its background tasks."""
        if self._health_task:
            self._health_task.cancel()
            try:
                await self._health_task
            except asyncio.CancelledError:
                pass
            self._health_task = None
            logger.info("Key manager stopped")
    
    def reload_config(self) -> None:
        """Reload the key configuration from file."""
        try:
            self._config = load_keys_config()
            logger.info(
                f"Loaded configuration: {len(self._config.upstream_keys)} upstream keys, "
                f"{len(self._config.client_keys)} client keys"
            )
        except Exception as e:
            logger.error(f"Failed to load key configuration: {e}")
            if self._config is None:
                # Create empty config as fallback
                self._config = KeysConfig()
    
    def save_config(self) -> None:
        """Save the current configuration to file."""
        if self._config:
            try:
                self._config.update_timestamp()
                save_keys_config(self._config)
                logger.debug("Key configuration saved")
            except Exception as e:
                logger.error(f"Failed to save key configuration: {e}")
    
    @property
    def config(self) -> KeysConfig:
        """Get the current configuration."""
        if self._config is None:
            self.reload_config()
        return self._config
    
    def get_next_upstream_key(self, strategy_name: Optional[str] = None) -> UpstreamKey:
        """
        Get the next upstream key using the specified or default strategy.
        
        Args:
            strategy_name: Optional strategy name to use
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        available_keys = self.config.get_active_upstream_keys()
        
        if not available_keys:
            raise ValueError("No active upstream keys available")
        
        # Use strategy manager to select key
        selected_key = strategy_manager.select_key(available_keys, strategy_name)
        
        # Mark key as used
        selected_key.mark_used()
        self.save_config()
        
        log_key_event(logger, "selected", selected_key.key)
        return selected_key
    
    def mark_key_failed(self, key: UpstreamKey) -> None:
        """
        Mark a key as failed and handle failure logic.
        
        Args:
            key: The key that failed
        """
        if key.permanently_disabled:
            return  # Already permanently disabled
        
        # Mark the failure
        key.mark_failed()
        
        # Check if we need to disable the key
        if key.failed_count >= settings.failure_threshold:
            if key.recovery_attempted:
                # Permanently disable after recovery attempt failed
                key.disable_permanently()
                log_key_event(
                    logger, "permanently_disabled", key.key,
                    f"Failed {key.failed_count} times after recovery attempt"
                )
            else:
                # Temporarily disable for first time
                key.disable_temporarily()
                log_key_event(
                    logger, "temporarily_disabled", key.key,
                    f"Failed {key.failed_count} times, will attempt recovery"
                )
        else:
            log_key_event(
                logger, "failed", key.key,
                f"Failure count: {key.failed_count}/{settings.failure_threshold}"
            )
        
        self.save_config()
    
    def verify_client_key(self, key: str) -> bool:
        """
        Verify that a client key is valid and enabled.
        
        Args:
            key: Client key to verify
            
        Returns:
            True if key is valid and enabled, False otherwise
        """
        client_key = self.config.get_client_key_by_value(key)
        
        if client_key and client_key.enabled:
            client_key.mark_used()
            self.save_config()
            return True
        
        return False
    
    def get_upstream_key_by_suffix(self, suffix: str) -> Optional[UpstreamKey]:
        """
        Find an upstream key by its suffix.
        
        Args:
            suffix: Key suffix to search for
            
        Returns:
            UpstreamKey if found, None otherwise
        """
        return self.config.get_upstream_key_by_suffix(suffix)
    
    def get_client_key_by_value(self, key_value: str) -> Optional[ClientKey]:
        """
        Find a client key by its value.
        
        Args:
            key_value: Key value to search for
            
        Returns:
            ClientKey if found, None otherwise
        """
        return self.config.get_client_key_by_value(key_value)
    
    def get_statistics(self) -> KeyStatistics:
        """
        Get current key statistics.
        
        Returns:
            KeyStatistics object with current stats
        """
        return KeyStatistics.from_config(self.config)
    
    def reset_key_status(self, key_suffix: str) -> bool:
        """
        Reset the status of a key identified by its suffix.
        
        Args:
            key_suffix: Suffix of the key to reset
            
        Returns:
            True if key was found and reset, False otherwise
        """
        key = self.get_upstream_key_by_suffix(key_suffix)
        
        if key:
            key.permanently_disabled = False
            key.recovery_attempted = False
            key.enabled = True
            key.reset_failures()
            
            self.save_config()
            log_key_event(logger, "reset", key.key, "Status reset manually")
            return True
        
        return False
    
    def disable_key(self, key_suffix: str) -> bool:
        """
        Manually disable a key identified by its suffix.
        
        Args:
            key_suffix: Suffix of the key to disable
            
        Returns:
            True if key was found and disabled, False otherwise
        """
        key = self.get_upstream_key_by_suffix(key_suffix)
        
        if key:
            key.disable_temporarily()
            self.save_config()
            log_key_event(logger, "disabled", key.key, "Disabled manually")
            return True
        
        return False
    
    def enable_key(self, key_suffix: str) -> bool:
        """
        Manually enable a key identified by its suffix.
        
        Args:
            key_suffix: Suffix of the key to enable
            
        Returns:
            True if key was found and enabled, False otherwise
        """
        key = self.get_upstream_key_by_suffix(key_suffix)
        
        if key:
            if key.permanently_disabled:
                return False  # Cannot enable permanently disabled keys
            
            key.enable()
            self.save_config()
            log_key_event(logger, "enabled", key.key, "Enabled manually")
            return True
        
        return False
    
    def get_load_balancer_strategy(self) -> str:
        """
        Get the current load balancing strategy name.
        
        Returns:
            Current strategy name
        """
        return strategy_manager.get_current_strategy_name()
    
    def set_load_balancer_strategy(self, strategy_name: str) -> None:
        """
        Set the load balancing strategy.
        
        Args:
            strategy_name: Name of the strategy to set
            
        Raises:
            ValueError: If strategy name is invalid
        """
        if not strategy_manager.validate_strategy_name(strategy_name):
            valid_strategies = strategy_manager.list_strategies()
            raise ValueError(
                f"Invalid strategy '{strategy_name}'. "
                f"Valid strategies: {', '.join(valid_strategies)}"
            )
        
        old_strategy = strategy_manager.get_current_strategy_name()
        strategy_manager.set_current_strategy(strategy_name)
        
        logger.info(f"Load balancing strategy changed: {old_strategy} -> {strategy_name}")
    
    def get_available_strategies(self) -> List[str]:
        """
        Get a list of available load balancing strategies.
        
        Returns:
            List of strategy names
        """
        return strategy_manager.list_strategies()


# Global key manager instance
key_manager = KeyManager()
