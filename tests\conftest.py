"""
Pytest configuration and shared fixtures.

This module contains pytest configuration and fixtures that are shared
across multiple test modules.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from typing import Dict, Generator, List
from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest
from fastapi.testclient import TestClient

from src.config import settings
from src.key_manager import KeyManager
from src.models import AnthropicModel, ClientKey, KeysConfig, UpstreamKey


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_upstream_keys() -> List[UpstreamKey]:
    """Create sample upstream keys for testing."""
    return [
        UpstreamKey(
            key="test_key_1_abcd",
            weight=1,
            enabled=True,
            description="Test key 1"
        ),
        UpstreamKey(
            key="test_key_2_efgh",
            weight=2,
            enabled=True,
            description="Test key 2"
        ),
        UpstreamKey(
            key="test_key_3_ijkl",
            weight=1,
            enabled=False,
            failed_count=3,
            description="Disabled test key"
        ),
    ]


@pytest.fixture
def sample_client_keys() -> List[ClientKey]:
    """Create sample client keys for testing."""
    return [
        ClientKey(
            key="client_key_1",
            enabled=True,
            description="Test client key 1"
        ),
        ClientKey(
            key="client_key_2",
            enabled=True,
            description="Test client key 2"
        ),
        ClientKey(
            key="disabled_client_key",
            enabled=False,
            description="Disabled client key"
        ),
    ]


@pytest.fixture
def sample_keys_config(
    sample_upstream_keys: List[UpstreamKey],
    sample_client_keys: List[ClientKey]
) -> KeysConfig:
    """Create a sample keys configuration."""
    return KeysConfig(
        upstream_keys=sample_upstream_keys,
        client_keys=sample_client_keys
    )


@pytest.fixture
def sample_models() -> List[AnthropicModel]:
    """Create sample models for testing."""
    return [
        AnthropicModel(
            id="claude-3-sonnet-20240229",
            object="model",
            owned_by="anthropic"
        ),
        AnthropicModel(
            id="claude-3-haiku-20240307",
            object="model",
            owned_by="anthropic"
        ),
    ]


@pytest.fixture
def keys_config_file(temp_dir: Path, sample_keys_config: KeysConfig) -> Path:
    """Create a temporary keys configuration file."""
    config_file = temp_dir / "test_keys.json"
    with open(config_file, 'w') as f:
        json.dump(sample_keys_config.dict(), f, indent=2, default=str)
    return config_file


@pytest.fixture
def models_config_file(temp_dir: Path, sample_models: List[AnthropicModel]) -> Path:
    """Create a temporary models configuration file."""
    config_file = temp_dir / "test_models.json"
    models_data = [model.dict() for model in sample_models]
    with open(config_file, 'w') as f:
        json.dump(models_data, f, indent=2)
    return config_file


@pytest.fixture
def mock_settings(keys_config_file: Path, models_config_file: Path):
    """Mock settings with test configuration files."""
    original_keys_file = settings.keys_config_file
    original_models_file = settings.models_config_file
    
    settings.keys_config_file = str(keys_config_file)
    settings.models_config_file = str(models_config_file)
    
    yield settings
    
    # Restore original settings
    settings.keys_config_file = original_keys_file
    settings.models_config_file = original_models_file


@pytest.fixture
def mock_http_client():
    """Create a mock HTTP client for testing."""
    mock_client = AsyncMock(spec=httpx.AsyncClient)
    
    # Mock successful response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.content = b'{"test": "response"}'
    mock_response.headers = {"content-type": "application/json"}
    
    mock_client.post.return_value = mock_response
    mock_client.stream.return_value.__aenter__.return_value = mock_response
    mock_response.aiter_bytes.return_value = [b'chunk1', b'chunk2']
    
    return mock_client


@pytest.fixture
def key_manager_with_test_config(mock_settings) -> KeyManager:
    """Create a key manager with test configuration."""
    manager = KeyManager()
    manager.reload_config()
    return manager


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI application."""
    from src.main import create_app
    
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_anthropic_request() -> Dict:
    """Create a sample Anthropic API request."""
    return {
        "model": "claude-3-sonnet-20240229",
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }


@pytest.fixture
def sample_anthropic_streaming_request(sample_anthropic_request: Dict) -> Dict:
    """Create a sample streaming Anthropic API request."""
    request = sample_anthropic_request.copy()
    request["stream"] = True
    return request


@pytest.fixture
def mock_anthropic_response() -> Dict:
    """Create a mock Anthropic API response."""
    return {
        "id": "msg_test123",
        "type": "message",
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "Hello! I'm doing well, thank you for asking."
            }
        ],
        "model": "claude-3-sonnet-20240229",
        "stop_reason": "end_turn",
        "usage": {
            "input_tokens": 10,
            "output_tokens": 15,
            "total_tokens": 25
        }
    }


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test."""
    # Reset any global variables or singletons here
    yield
    # Cleanup after test if needed


class MockAsyncContextManager:
    """Mock async context manager for testing."""
    
    def __init__(self, return_value):
        self.return_value = return_value
    
    async def __aenter__(self):
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


@pytest.fixture
def mock_stream_response():
    """Create a mock streaming response."""
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.aiter_bytes.return_value = [
        b'data: {"type": "message_start"}\n\n',
        b'data: {"type": "content_block_delta", "delta": {"text": "Hello"}}\n\n',
        b'data: {"type": "message_stop"}\n\n'
    ]
    return MockAsyncContextManager(mock_response)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
