"""
Health check and status API routes.

This module contains endpoints for checking the health and status
of the proxy server, including public endpoints that don't require authentication.
"""

import logging
from typing import Optional

from fastapi import APIRouter, Depends

from ..config import settings
from ..key_manager import key_manager
from ..models import APIResponse, HealthStatus
from ..utils import get_current_datetime, get_logger
from .dependencies import get_optional_client_key

logger = get_logger(__name__)

# Create router
router = APIRouter(tags=["health"])


@router.get("/")
async def root_health_check() -> APIResponse:
    """
    Root endpoint for basic health check.
    
    This endpoint provides a quick health check without requiring authentication.
    It's useful for load balancers and monitoring systems.
    
    Returns:
        Basic health status and system information
    """
    try:
        stats = key_manager.get_statistics()
        
        return APIResponse(
            success=True,
            message="Service is healthy",
            data={
                "status": "ok",
                "version": settings.app_version,
                "features": [
                    "strict_key_management",
                    "load_balancing", 
                    "auto_retry",
                    "health_check"
                ],
                "config": {
                    "strategy": key_manager.get_load_balancer_strategy(),
                    "max_retries": settings.max_retries,
                    "health_check_interval": settings.health_check_interval,
                    "upstream_keys": stats.total_upstream_keys,
                    "client_keys": stats.total_client_keys,
                    "models_loaded": 0  # TODO: Get actual models count
                },
                "key_summary": {
                    "total": stats.total_upstream_keys,
                    "active": stats.active_upstream_keys,
                    "temporarily_disabled": stats.temporarily_disabled_keys,
                    "permanently_disabled": stats.permanently_disabled_keys
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error in root health check: {e}")
        return APIResponse(
            success=False,
            message="Service health check failed",
            error=str(e)
        )


@router.get("/health")
async def detailed_health_check(
    client_key: Optional[str] = Depends(get_optional_client_key)
) -> APIResponse:
    """
    Detailed health check endpoint.
    
    This endpoint provides comprehensive health information.
    If a valid client key is provided, more detailed information is returned.
    
    Args:
        client_key: Optional client API key for detailed information
        
    Returns:
        Detailed health status and system information
    """
    try:
        stats = key_manager.get_statistics()
        is_authenticated = client_key and key_manager.verify_client_key(client_key)
        
        health_data = {
            "status": "healthy",
            "version": settings.app_version,
            "timestamp": get_current_datetime(),
            "load_balancer": {
                "strategy": key_manager.get_load_balancer_strategy(),
                "available_strategies": key_manager.get_available_strategies()
            },
            "key_summary": stats.dict(),
            "config": {
                "max_retries": settings.max_retries,
                "retry_delay": settings.retry_delay,
                "health_check_interval": settings.health_check_interval,
                "failure_threshold": settings.failure_threshold,
                "recovery_time": settings.recovery_time
            }
        }
        
        # Add detailed information if authenticated
        if is_authenticated:
            config = key_manager.config
            upstream_keys_details = []
            
            for key in config.upstream_keys:
                upstream_keys_details.append({
                    "key_suffix": key.masked_key,
                    "enabled": key.enabled,
                    "permanently_disabled": key.permanently_disabled,
                    "recovery_attempted": key.recovery_attempted,
                    "requests": key.total_requests,
                    "failures": key.failed_count,
                    "last_failed": key.last_failed_time,
                    "weight": key.weight,
                    "status": (
                        "permanently_disabled" if key.permanently_disabled
                        else "active" if key.enabled
                        else "temporarily_disabled"
                    ),
                    "description": key.description
                })
            
            health_data["upstream_keys"] = upstream_keys_details
            
            # Add health monitoring information if available
            if hasattr(key_manager, '_health_monitor') and key_manager._health_monitor:
                health_data["health_monitoring"] = key_manager._health_monitor.get_health_summary()
        
        return APIResponse(
            success=True,
            message="Detailed health check completed",
            data=health_data
        )
        
    except Exception as e:
        logger.error(f"Error in detailed health check: {e}")
        return APIResponse(
            success=False,
            message="Detailed health check failed",
            error=str(e)
        )


@router.get("/status")
async def service_status() -> HealthStatus:
    """
    Simple service status endpoint.
    
    This endpoint returns a standardized health status object
    that can be used by monitoring systems.
    
    Returns:
        HealthStatus object with current service status
    """
    try:
        stats = key_manager.get_statistics()
        
        # Determine overall status based on available keys
        if stats.active_upstream_keys == 0:
            status = "unhealthy"
        elif stats.active_upstream_keys < stats.total_upstream_keys / 2:
            status = "degraded"
        else:
            status = "healthy"
        
        return HealthStatus(
            status=status,
            version=settings.app_version,
            timestamp=get_current_datetime()
        )
        
    except Exception as e:
        logger.error(f"Error getting service status: {e}")
        return HealthStatus(
            status="error",
            version=settings.app_version,
            timestamp=get_current_datetime()
        )


@router.get("/ping")
async def ping() -> dict:
    """
    Simple ping endpoint for basic connectivity testing.
    
    Returns:
        Simple pong response
    """
    return {"message": "pong", "timestamp": get_current_datetime()}


@router.get("/version")
async def get_version() -> dict:
    """
    Get application version information.
    
    Returns:
        Version and build information
    """
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": settings.app_description
    }


@router.get("/metrics")
async def get_metrics(
    client_key: Optional[str] = Depends(get_optional_client_key)
) -> APIResponse:
    """
    Get basic metrics for monitoring.
    
    This endpoint provides metrics that can be consumed by monitoring
    systems like Prometheus. Authentication is optional but provides
    more detailed metrics.
    
    Args:
        client_key: Optional client API key for detailed metrics
        
    Returns:
        Metrics data
    """
    try:
        stats = key_manager.get_statistics()
        is_authenticated = client_key and key_manager.verify_client_key(client_key)
        
        metrics = {
            "upstream_keys_total": stats.total_upstream_keys,
            "upstream_keys_active": stats.active_upstream_keys,
            "upstream_keys_temporarily_disabled": stats.temporarily_disabled_keys,
            "upstream_keys_permanently_disabled": stats.permanently_disabled_keys,
            "client_keys_total": stats.total_client_keys,
            "client_keys_enabled": stats.enabled_client_keys,
            "requests_total": stats.total_requests,
            "failures_total": stats.total_failures,
            "load_balancer_strategy": key_manager.get_load_balancer_strategy()
        }
        
        # Add detailed metrics if authenticated
        if is_authenticated:
            config = key_manager.config
            
            # Per-key metrics
            key_metrics = {}
            for i, key in enumerate(config.upstream_keys):
                key_id = f"key_{i}"
                key_metrics[key_id] = {
                    "enabled": int(key.enabled),
                    "permanently_disabled": int(key.permanently_disabled),
                    "total_requests": key.total_requests,
                    "failed_count": key.failed_count,
                    "weight": key.weight
                }
            
            metrics["key_details"] = key_metrics
            
            # Health monitoring metrics
            if hasattr(key_manager, '_health_monitor') and key_manager._health_monitor:
                health_summary = key_manager._health_monitor.get_health_summary()
                metrics["health_monitoring"] = health_summary
        
        return APIResponse(
            success=True,
            message="Metrics retrieved successfully",
            data=metrics
        )
        
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return APIResponse(
            success=False,
            message="Failed to retrieve metrics",
            error=str(e)
        )
