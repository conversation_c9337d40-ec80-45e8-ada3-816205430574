"""
AMP 0.3 - Anthropic API Proxy Server
Modularized version with improved architecture

This package provides a comprehensive API proxy for Anthropic's Claude API
with advanced features including:

- Intelligent load balancing across multiple API keys
- Automatic failure detection and recovery
- Health monitoring and statistics
- Administrative management interface
- Comprehensive logging and error handling
- Modular, testable architecture

Main Components:
- config: Configuration management and settings
- models: Data models and type definitions
- key_manager: API key management and health monitoring
- load_balancer: Load balancing strategies
- routers: FastAPI route handlers
- utils: Utility functions and helpers
- main: Application entry point and setup
"""

from .main import app, create_app, main

__version__ = "4.7.0"
__title__ = "Anthropic API Proxy"
__description__ = "Advanced Anthropic API Proxy with Key Management"
__author__ = "AMP Team"

__all__ = [
    "app",
    "create_app",
    "main",
    "__version__",
    "__title__",
    "__description__",
    "__author__",
]
