"""
Logging utility functions.

This module provides utilities for setting up and managing logging
throughout the application.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from ..config import settings


def setup_logging(
    level: Optional[str] = None,
    log_file: Optional[Path] = None,
    format_string: Optional[str] = None
) -> None:
    """
    Set up application logging configuration.
    
    Args:
        level: Logging level (defaults to settings.log_level)
        log_file: Path to log file (defaults to settings.log_file_path)
        format_string: Log format string (defaults to settings.log_format)
    """
    # Use settings defaults if not provided
    level = level or settings.log_level
    log_file = log_file or settings.log_file_path
    format_string = format_string or settings.log_format
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        try:
            # Create directory if it doesn't exist
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Use rotating file handler to prevent huge log files
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            logging.info(f"Logging to file: {log_file}")
            
        except Exception as e:
            logging.warning(f"Could not set up file logging: {e}")
    
    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    
    logging.info(f"Logging configured with level: {level}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def log_request_start(
    logger: logging.Logger,
    method: str,
    path: str,
    client_key: Optional[str] = None,
    upstream_key: Optional[str] = None
) -> None:
    """
    Log the start of a request.
    
    Args:
        logger: Logger instance
        method: HTTP method
        path: Request path
        client_key: Client API key (will be masked)
        upstream_key: Upstream API key (will be masked)
    """
    from .keys import sanitize_key_for_logging
    
    client_info = ""
    if client_key:
        client_info = f" client={sanitize_key_for_logging(client_key)}"
    
    upstream_info = ""
    if upstream_key:
        upstream_info = f" upstream={sanitize_key_for_logging(upstream_key)}"
    
    logger.info(f"Request started: {method} {path}{client_info}{upstream_info}")


def log_request_end(
    logger: logging.Logger,
    method: str,
    path: str,
    status_code: int,
    processing_time: float,
    upstream_key: Optional[str] = None
) -> None:
    """
    Log the end of a request.
    
    Args:
        logger: Logger instance
        method: HTTP method
        path: Request path
        status_code: HTTP status code
        processing_time: Processing time in seconds
        upstream_key: Upstream API key (will be masked)
    """
    from .keys import sanitize_key_for_logging
    
    upstream_info = ""
    if upstream_key:
        upstream_info = f" upstream={sanitize_key_for_logging(upstream_key)}"
    
    logger.info(
        f"Request completed: {method} {path} "
        f"status={status_code} time={processing_time:.3f}s{upstream_info}"
    )


def log_key_event(
    logger: logging.Logger,
    event: str,
    key: str,
    details: Optional[str] = None
) -> None:
    """
    Log a key-related event.
    
    Args:
        logger: Logger instance
        event: Event description
        key: API key (will be masked)
        details: Additional details
    """
    from .keys import sanitize_key_for_logging
    
    key_info = sanitize_key_for_logging(key)
    details_info = f" - {details}" if details else ""
    
    logger.info(f"Key event: {event} {key_info}{details_info}")


def log_strategy_change(
    logger: logging.Logger,
    old_strategy: str,
    new_strategy: str
) -> None:
    """
    Log a load balancing strategy change.
    
    Args:
        logger: Logger instance
        old_strategy: Previous strategy name
        new_strategy: New strategy name
    """
    logger.info(f"Load balancing strategy changed: {old_strategy} -> {new_strategy}")


def log_error_with_context(
    logger: logging.Logger,
    error: Exception,
    context: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log an error with additional context.
    
    Args:
        logger: Logger instance
        error: Exception that occurred
        context: Additional context information
        **kwargs: Additional key-value pairs to log
    """
    context_info = f" Context: {context}" if context else ""
    
    extra_info = ""
    if kwargs:
        extra_parts = [f"{k}={v}" for k, v in kwargs.items()]
        extra_info = f" Extra: {', '.join(extra_parts)}"
    
    logger.error(f"Error: {error}{context_info}{extra_info}", exc_info=True)


def log_health_check(
    logger: logging.Logger,
    total_keys: int,
    active_keys: int,
    disabled_keys: int,
    failed_keys: int
) -> None:
    """
    Log health check results.
    
    Args:
        logger: Logger instance
        total_keys: Total number of keys
        active_keys: Number of active keys
        disabled_keys: Number of disabled keys
        failed_keys: Number of failed keys
    """
    logger.info(
        f"Health check: total={total_keys} active={active_keys} "
        f"disabled={disabled_keys} failed={failed_keys}"
    )


def log_configuration_loaded(
    logger: logging.Logger,
    config_type: str,
    file_path: Path,
    item_count: Optional[int] = None
) -> None:
    """
    Log successful configuration loading.
    
    Args:
        logger: Logger instance
        config_type: Type of configuration (e.g., "keys", "models")
        file_path: Path to the configuration file
        item_count: Number of items loaded (optional)
    """
    count_info = f" ({item_count} items)" if item_count is not None else ""
    logger.info(f"Configuration loaded: {config_type} from {file_path}{count_info}")


def log_configuration_saved(
    logger: logging.Logger,
    config_type: str,
    file_path: Path,
    item_count: Optional[int] = None
) -> None:
    """
    Log successful configuration saving.
    
    Args:
        logger: Logger instance
        config_type: Type of configuration (e.g., "keys", "models")
        file_path: Path to the configuration file
        item_count: Number of items saved (optional)
    """
    count_info = f" ({item_count} items)" if item_count is not None else ""
    logger.info(f"Configuration saved: {config_type} to {file_path}{count_info}")


class ContextualLogger:
    """
    A logger wrapper that adds contextual information to all log messages.
    
    This is useful for adding request IDs, user information, or other
    contextual data to log messages automatically.
    """
    
    def __init__(self, logger: logging.Logger, **context):
        """
        Initialize the contextual logger.
        
        Args:
            logger: Base logger instance
            **context: Context key-value pairs to add to all messages
        """
        self.logger = logger
        self.context = context
    
    def _format_message(self, message: str) -> str:
        """Format a message with context information."""
        if not self.context:
            return message
        
        context_parts = [f"{k}={v}" for k, v in self.context.items()]
        context_str = " ".join(context_parts)
        return f"[{context_str}] {message}"
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """Log a debug message with context."""
        self.logger.debug(self._format_message(message), *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        """Log an info message with context."""
        self.logger.info(self._format_message(message), *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """Log a warning message with context."""
        self.logger.warning(self._format_message(message), *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        """Log an error message with context."""
        self.logger.error(self._format_message(message), *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs) -> None:
        """Log a critical message with context."""
        self.logger.critical(self._format_message(message), *args, **kwargs)
