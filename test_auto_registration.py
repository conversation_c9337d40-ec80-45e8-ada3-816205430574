#!/usr/bin/env python3
"""
Test script for auto registration functionality.

This script tests the automatic key registration feature
by simulating key failures and monitoring the system response.
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import requests
from src.config import settings
from src.key_manager import key_manager
from src.utils import get_logger, setup_logging


def print_section(title: str):
    """Print a section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_success(message: str):
    """Print a success message."""
    print(f"✅ {message}")


def print_error(message: str):
    """Print an error message."""
    print(f"❌ {message}")


def print_info(message: str):
    """Print an info message."""
    print(f"ℹ️  {message}")


def get_system_stats():
    """Get current system statistics."""
    try:
        response = requests.get(
            "http://127.0.0.1:8000/admin/stats",
            headers={"Authorization": "Bearer sk-hiclover"}
        )
        if response.status_code == 200:
            return response.json()["data"]["key_summary"]
        else:
            print_error(f"Failed to get stats: {response.status_code}")
            return None
    except Exception as e:
        print_error(f"Error getting stats: {e}")
        return None


def get_auto_registration_config():
    """Get auto registration configuration."""
    try:
        response = requests.get(
            "http://127.0.0.1:8000/admin/keys/auto-registration/config",
            headers={"Authorization": "Bearer sk-hiclover"}
        )
        if response.status_code == 200:
            return response.json()["data"]
        else:
            print_error(f"Failed to get auto registration config: {response.status_code}")
            return None
    except Exception as e:
        print_error(f"Error getting auto registration config: {e}")
        return None


def trigger_manual_registration(count: int):
    """Trigger manual key registration."""
    try:
        response = requests.post(
            "http://127.0.0.1:8000/admin/keys/register",
            headers={
                "Authorization": "Bearer sk-hiclover",
                "Content-Type": "application/json"
            },
            json={"count": count}
        )
        if response.status_code == 200:
            return response.json()
        else:
            print_error(f"Failed to trigger registration: {response.status_code}")
            print_error(f"Response: {response.text}")
            return None
    except Exception as e:
        print_error(f"Error triggering registration: {e}")
        return None


def update_auto_registration_config(enable: bool, min_keys: int):
    """Update auto registration configuration."""
    try:
        response = requests.post(
            "http://127.0.0.1:8000/admin/keys/auto-registration/config",
            headers={
                "Authorization": "Bearer sk-hiclover",
                "Content-Type": "application/json"
            },
            json={
                "enable_auto_registration": enable,
                "min_active_keys": min_keys,
                "max_registration_attempts": 3
            }
        )
        if response.status_code == 200:
            return response.json()
        else:
            print_error(f"Failed to update config: {response.status_code}")
            return None
    except Exception as e:
        print_error(f"Error updating config: {e}")
        return None


def simulate_key_failures():
    """Simulate key failures by disabling keys."""
    try:
        # Get current stats
        stats = get_system_stats()
        if not stats:
            return False
        
        print_info(f"Current active keys: {stats['active_upstream_keys']}")
        
        # Get keys status to find keys to disable
        response = requests.get(
            "http://127.0.0.1:8000/admin/keys/status",
            headers={"Authorization": "Bearer sk-hiclover"}
        )
        
        if response.status_code != 200:
            print_error("Failed to get keys status")
            return False
        
        keys_data = response.json()["data"]["keys"]
        active_keys = [key for key in keys_data if key["enabled"]]
        
        if len(active_keys) < 2:
            print_info("Not enough active keys to simulate failures")
            return True
        
        # Disable some keys to trigger auto registration
        keys_to_disable = active_keys[:2]  # Disable first 2 active keys
        
        for key_info in keys_to_disable:
            key_suffix = key_info["key_suffix"]
            response = requests.post(
                f"http://127.0.0.1:8000/admin/keys/{key_suffix}/disable",
                headers={"Authorization": "Bearer sk-hiclover"}
            )
            
            if response.status_code == 200:
                print_info(f"Disabled key: ...{key_suffix}")
            else:
                print_error(f"Failed to disable key: ...{key_suffix}")
        
        return True
        
    except Exception as e:
        print_error(f"Error simulating failures: {e}")
        return False


async def main():
    """Main test function."""
    print_section("Auto Registration Feature Test")
    
    # Setup logging
    setup_logging()
    
    print_info("Testing automatic key registration functionality...")
    
    # 1. Check initial configuration
    print_section("1. Initial Configuration Check")
    
    config = get_auto_registration_config()
    if config:
        print_info(f"Auto registration enabled: {config.get('enable_auto_registration')}")
        print_info(f"Minimum active keys: {config.get('min_active_keys')}")
        print_info(f"Runtime enabled: {config.get('runtime_enabled')}")
        print_info(f"Registrar available: {config.get('registrar_available')}")
    else:
        print_error("Failed to get initial configuration")
        return
    
    # 2. Check initial system stats
    print_section("2. Initial System Statistics")
    
    initial_stats = get_system_stats()
    if initial_stats:
        print_info(f"Total upstream keys: {initial_stats['total_upstream_keys']}")
        print_info(f"Active upstream keys: {initial_stats['active_upstream_keys']}")
        print_info(f"Temporarily disabled: {initial_stats['temporarily_disabled_keys']}")
        print_info(f"Permanently disabled: {initial_stats['permanently_disabled_keys']}")
    else:
        print_error("Failed to get initial statistics")
        return
    
    # 3. Test configuration update
    print_section("3. Configuration Update Test")
    
    update_result = update_auto_registration_config(enable=True, min_keys=3)
    if update_result and update_result.get("success"):
        print_success("Successfully updated auto registration configuration")
    else:
        print_error("Failed to update configuration")
    
    # 4. Test manual registration (if needed)
    print_section("4. Manual Registration Test")
    
    if initial_stats["active_upstream_keys"] < 3:
        needed_keys = 3 - initial_stats["active_upstream_keys"]
        print_info(f"Need {needed_keys} more keys to reach minimum threshold")
        
        # Note: Manual registration requires playwright and may take time
        print_info("Manual registration test skipped (requires playwright setup)")
        print_info("To test manual registration, ensure playwright is installed:")
        print_info("  pip install playwright")
        print_info("  playwright install chromium")
        
        # Uncomment the following lines to test manual registration:
        # registration_result = trigger_manual_registration(needed_keys)
        # if registration_result and registration_result.get("success"):
        #     print_success(f"Successfully registered {registration_result['data']['registered_count']} keys")
        # else:
        #     print_error("Manual registration failed")
    else:
        print_info("Sufficient active keys available, skipping manual registration")
    
    # 5. Test key failure simulation
    print_section("5. Key Failure Simulation")
    
    print_info("Simulating key failures to test auto registration trigger...")
    if simulate_key_failures():
        print_success("Key failure simulation completed")
        
        # Wait a bit and check stats again
        print_info("Waiting 10 seconds for health monitor to detect changes...")
        time.sleep(10)
        
        updated_stats = get_system_stats()
        if updated_stats:
            print_info(f"Updated active keys: {updated_stats['active_upstream_keys']}")
            if updated_stats['active_upstream_keys'] < initial_stats['active_upstream_keys']:
                print_success("Key failures detected successfully")
            else:
                print_info("No change in active keys count")
        
    else:
        print_error("Key failure simulation failed")
    
    # 6. Summary
    print_section("6. Test Summary")
    
    final_stats = get_system_stats()
    final_config = get_auto_registration_config()
    
    if final_stats and final_config:
        print_success("Auto registration feature is properly integrated")
        print_info("Key features verified:")
        print_info("  ✓ Configuration management")
        print_info("  ✓ Statistics reporting")
        print_info("  ✓ Key failure simulation")
        print_info("  ✓ API endpoint integration")
        
        if final_config.get('registrar_available'):
            print_info("  ✓ Auto registrar available")
        else:
            print_info("  ⚠ Auto registrar not available (may need playwright)")
        
        print_info("\nTo fully test auto registration:")
        print_info("1. Install playwright: pip install playwright")
        print_info("2. Install browser: playwright install chromium")
        print_info("3. Ensure sufficient active keys fall below threshold")
        print_info("4. Wait for health monitor to trigger auto registration")
        
    else:
        print_error("Some features may not be working correctly")
    
    print_section("Test Completed")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\n\nUnexpected error during test: {e}")
        sys.exit(1)
