"""
Base models and common types for the Anthropic API Proxy.

This module contains the foundational data models used throughout the application,
including key management, configuration, and API request/response models.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class KeyStatus(str, Enum):
    """Enumeration of possible key statuses."""
    
    ACTIVE = "active"
    TEMPORARILY_DISABLED = "temporarily_disabled"
    PERMANENTLY_DISABLED = "permanently_disabled"


class LoadBalancerStrategy(str, Enum):
    """Enumeration of available load balancer strategies."""
    
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    RANDOM = "random"
    LEAST_USED = "least_used"


class BaseConfigModel(BaseModel):
    """Base configuration model with common functionality."""
    
    class Config:
        """Pydantic configuration."""
        
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class HealthStatus(BaseModel):
    """Health status information."""
    
    status: str = Field(..., description="Overall health status")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(default_factory=datetime.now, description="Status timestamp")
    uptime_seconds: Optional[float] = Field(None, description="Application uptime in seconds")


class APIResponse(BaseModel):
    """Standard API response wrapper."""
    
    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if any")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")


class PaginationParams(BaseModel):
    """Pagination parameters for list endpoints."""
    
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(20, ge=1, le=100, description="Number of items per page")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseModel):
    """Paginated response wrapper."""
    
    items: List[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    
    @validator('total_pages', always=True)
    def calculate_total_pages(cls, v, values):
        """Calculate total pages based on total items and page size."""
        total = values.get('total', 0)
        page_size = values.get('page_size', 1)
        return (total + page_size - 1) // page_size if total > 0 else 0
