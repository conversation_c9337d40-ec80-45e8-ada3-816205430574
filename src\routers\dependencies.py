"""
FastAPI dependencies for authentication and common functionality.

This module contains dependency functions used across different routers
for authentication, validation, and common request processing.
"""

import logging
from typing import Optional

from fastapi import Depends, HTTPException, Request

from ..key_manager import key_manager
from ..utils import get_logger, sanitize_key_for_logging

logger = get_logger(__name__)


async def verify_client_api_key(request: Request) -> str:
    """
    Dependency to verify client API key from request headers.
    
    This function extracts and validates the client API key from various
    possible header locations and ensures it's valid and enabled.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Validated client API key
        
    Raises:
        HTTPException: If key is missing or invalid
    """
    api_key = None
    
    # Try to extract API key from various header locations
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.startswith("Bearer "):
        api_key = auth_header[7:]  # Remove "Bearer " prefix
    else:
        # Try other common header names
        api_key = (
            request.headers.get("x-api-key") or
            request.headers.get("anthropic-api-key") or
            request.headers.get("api-key")
        )
    
    if not api_key:
        logger.warning("Request missing API key")
        raise HTTPException(
            status_code=401,
            detail="Missing API key. Provide key in Authorization header (Bearer token) or x-api-key header."
        )
    
    # Verify the key with the key manager
    if not key_manager.verify_client_key(api_key):
        logger.warning(f"Invalid API key: {sanitize_key_for_logging(api_key)}")
        raise HTTPException(
            status_code=401,
            detail="Invalid API key. Please check your credentials."
        )
    
    logger.debug(f"Client authenticated: {sanitize_key_for_logging(api_key)}")
    return api_key


async def get_optional_client_key(request: Request) -> Optional[str]:
    """
    Dependency to optionally extract client API key without validation.
    
    This is useful for endpoints that may or may not require authentication.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Client API key if present, None otherwise
    """
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.startswith("Bearer "):
        return auth_header[7:]
    
    return (
        request.headers.get("x-api-key") or
        request.headers.get("anthropic-api-key") or
        request.headers.get("api-key")
    )


async def get_request_info(request: Request) -> dict:
    """
    Dependency to extract common request information.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Dictionary with request information
    """
    return {
        "method": request.method,
        "path": request.url.path,
        "query_params": dict(request.query_params),
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "content_type": request.headers.get("content-type"),
    }


class RateLimiter:
    """
    Simple rate limiter dependency (placeholder for future implementation).
    
    This class can be extended to implement actual rate limiting functionality
    based on client keys, IP addresses, or other criteria.
    """
    
    def __init__(self, requests_per_minute: int = 100):
        """
        Initialize the rate limiter.
        
        Args:
            requests_per_minute: Maximum requests allowed per minute
        """
        self.requests_per_minute = requests_per_minute
        # TODO: Implement actual rate limiting logic
    
    async def __call__(self, request: Request, client_key: str = Depends(verify_client_api_key)) -> None:
        """
        Check rate limit for the request.
        
        Args:
            request: FastAPI request object
            client_key: Validated client API key
            
        Raises:
            HTTPException: If rate limit is exceeded
        """
        # TODO: Implement rate limiting logic
        # For now, this is a placeholder that always passes
        pass


# Common dependency instances
rate_limiter = RateLimiter()


async def validate_json_request(request: Request) -> dict:
    """
    Dependency to validate and parse JSON request body.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Parsed JSON data
        
    Raises:
        HTTPException: If JSON is invalid or missing
    """
    try:
        json_data = await request.json()
        if not isinstance(json_data, dict):
            raise HTTPException(
                status_code=400,
                detail="Request body must be a JSON object"
            )
        return json_data
    except ValueError as e:
        logger.warning(f"Invalid JSON in request: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid JSON in request body"
        )
    except Exception as e:
        logger.error(f"Error parsing request body: {e}")
        raise HTTPException(
            status_code=400,
            detail="Error parsing request body"
        )


async def get_anthropic_headers(request: Request) -> dict:
    """
    Dependency to extract and prepare headers for Anthropic API requests.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Dictionary of headers for upstream requests
    """
    from ..config import settings
    
    headers = {
        'anthropic-version': settings.anthropic_version,
        'Content-Type': 'application/json',
    }
    
    # Copy relevant headers from the original request
    for header_name in ['user-agent', 'accept-encoding']:
        if header_name in request.headers:
            headers[header_name] = request.headers[header_name]
    
    return headers


async def log_request_start(
    request: Request,
    client_key: str = Depends(verify_client_api_key)
) -> None:
    """
    Dependency to log the start of a request.
    
    Args:
        request: FastAPI request object
        client_key: Validated client API key
    """
    from ..utils import log_request_start as log_start
    
    log_start(
        logger,
        request.method,
        request.url.path,
        client_key=client_key
    )


class RequestContext:
    """
    Request context holder for passing information between dependencies.
    
    This class can be used to store request-specific information that
    needs to be shared across multiple dependencies or route handlers.
    """
    
    def __init__(self):
        """Initialize the request context."""
        self.client_key: Optional[str] = None
        self.upstream_key: Optional[str] = None
        self.request_start_time: Optional[float] = None
        self.request_info: Optional[dict] = None
        self.is_streaming: bool = False
    
    def set_client_key(self, key: str) -> None:
        """Set the client key."""
        self.client_key = key
    
    def set_upstream_key(self, key: str) -> None:
        """Set the upstream key."""
        self.upstream_key = key
    
    def set_streaming(self, streaming: bool) -> None:
        """Set streaming flag."""
        self.is_streaming = streaming


async def get_request_context() -> RequestContext:
    """
    Dependency to get or create a request context.
    
    Returns:
        RequestContext instance
    """
    return RequestContext()
