"""
Concrete implementations of load balancing strategies.

This module contains all the specific load balancing strategy implementations
that can be used to distribute requests across upstream keys.
"""

import random
from typing import List

from ..models import UpstreamKey
from .base import IndexBasedStrategy, LoadBalancerStrategy


class RoundRobinStrategy(IndexBasedStrategy):
    """
    Round-robin load balancing strategy.
    
    This strategy cycles through available keys in order, ensuring
    equal distribution of requests across all keys.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "round_robin"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Round-robin strategy that cycles through keys in order"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Select the next key in round-robin order.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        current_index, _ = self.get_current_and_advance(len(available_keys))
        return available_keys[current_index]


class WeightedRoundRobinStrategy(IndexBasedStrategy):
    """
    Weighted round-robin load balancing strategy.
    
    This strategy considers the weight of each key, giving keys with higher
    weights more chances to be selected in each round.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "weighted_round_robin"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Weighted round-robin strategy that considers key weights"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Select the next key based on weighted round-robin.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        # Create weighted list
        weighted_keys = []
        for key in available_keys:
            weighted_keys.extend([key] * key.weight)
        
        if not weighted_keys:
            raise ValueError("No weighted keys available")
        
        current_index, _ = self.get_current_and_advance(len(weighted_keys))
        return weighted_keys[current_index]


class RandomStrategy(LoadBalancerStrategy):
    """
    Random load balancing strategy.
    
    This strategy randomly selects from available keys, providing
    good distribution over time without maintaining state.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "random"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Random strategy that randomly selects from available keys"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Randomly select a key from available keys.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        return random.choice(available_keys)


class WeightedRandomStrategy(LoadBalancerStrategy):
    """
    Weighted random load balancing strategy.
    
    This strategy randomly selects keys but considers their weights,
    giving keys with higher weights a higher probability of being selected.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "weighted_random"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Weighted random strategy that considers key weights in selection"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Randomly select a key based on weights.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        # Calculate total weight
        total_weight = sum(key.weight for key in available_keys)
        if total_weight <= 0:
            raise ValueError("Total weight must be positive")
        
        # Generate random number and select key
        rand_value = random.uniform(0, total_weight)
        current_weight = 0
        
        for key in available_keys:
            current_weight += key.weight
            if rand_value <= current_weight:
                return key
        
        # Fallback (should not happen)
        return available_keys[-1]


class LeastUsedStrategy(LoadBalancerStrategy):
    """
    Least used load balancing strategy.
    
    This strategy selects the key that has been used the least number of times,
    helping to balance the load more evenly over time.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "least_used"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Least used strategy that selects the key with fewest requests"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Select the key with the least number of requests.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        return min(available_keys, key=lambda k: k.total_requests)


class LeastFailedStrategy(LoadBalancerStrategy):
    """
    Least failed load balancing strategy.
    
    This strategy selects the key that has failed the least number of times,
    preferring more reliable keys.
    """
    
    @property
    def name(self) -> str:
        """Get the strategy name."""
        return "least_failed"
    
    @property
    def description(self) -> str:
        """Get strategy description."""
        return "Least failed strategy that selects the key with fewest failures"
    
    def select_key(self, keys: List[UpstreamKey], **kwargs) -> UpstreamKey:
        """
        Select the key with the least number of failures.
        
        Args:
            keys: List of available upstream keys
            **kwargs: Additional parameters (unused)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If no keys are available
        """
        self.validate_keys(keys)
        available_keys = self.filter_available_keys(keys)
        
        return min(available_keys, key=lambda k: k.failed_count)
