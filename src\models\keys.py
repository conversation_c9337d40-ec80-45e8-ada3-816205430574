"""
Key management data models.

This module contains all data models related to API key management,
including upstream keys, client keys, and their configurations.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import Field, field_validator

from .base import BaseConfigModel, KeyStatus


class UpstreamKey(BaseConfigModel):
    """
    Model representing an upstream API key used to communicate with the target service.
    
    Upstream keys are the actual API keys used to make requests to the Anthropic API.
    They include weight for load balancing, failure tracking, and recovery mechanisms.
    """
    
    key: str = Field(..., description="The actual API key", min_length=1)
    weight: int = Field(1, ge=1, le=100, description="Weight for load balancing (1-100)")
    enabled: bool = Field(True, description="Whether the key is currently enabled")
    failed_count: int = Field(0, ge=0, description="Number of consecutive failures")
    last_failed_time: Optional[datetime] = Field(
        None, description="Timestamp of the last failure"
    )
    total_requests: int = Field(0, ge=0, description="Total number of requests made with this key")
    recovery_attempted: bool = Field(
        False, description="Whether recovery has been attempted after failure"
    )
    permanently_disabled: bool = Field(
        False, description="Whether the key is permanently disabled"
    )
    created_at: datetime = Field(
        default_factory=datetime.now, description="When the key was added"
    )
    last_used_at: Optional[datetime] = Field(
        None, description="When the key was last used successfully"
    )
    description: str = Field("", description="Optional description for the key")
    
    @field_validator('key')
    @classmethod
    def validate_key_format(cls, v):
        """Validate that the key has a reasonable format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Key cannot be empty")
        return v.strip()
    
    @property
    def status(self) -> KeyStatus:
        """Get the current status of the key."""
        if self.permanently_disabled:
            return KeyStatus.PERMANENTLY_DISABLED
        elif not self.enabled:
            return KeyStatus.TEMPORARILY_DISABLED
        else:
            return KeyStatus.ACTIVE
    
    @property
    def masked_key(self) -> str:
        """Get a masked version of the key for logging/display."""
        if len(self.key) <= 4:
            return "****"
        return f"...{self.key[-4:]}"
    
    def mark_used(self) -> None:
        """Mark the key as used (update counters and timestamps)."""
        self.total_requests += 1
        self.last_used_at = datetime.now()
    
    def mark_failed(self) -> None:
        """Mark the key as failed (increment failure count and set timestamp)."""
        self.failed_count += 1
        self.last_failed_time = datetime.now()
    
    def reset_failures(self) -> None:
        """Reset failure-related fields."""
        self.failed_count = 0
        self.last_failed_time = None
        self.recovery_attempted = False
    
    def disable_temporarily(self) -> None:
        """Temporarily disable the key."""
        self.enabled = False
    
    def disable_permanently(self) -> None:
        """Permanently disable the key."""
        self.enabled = False
        self.permanently_disabled = True
    
    def enable(self) -> None:
        """Enable the key (only if not permanently disabled)."""
        if not self.permanently_disabled:
            self.enabled = True


class ClientKey(BaseConfigModel):
    """
    Model representing a client API key used for authentication.
    
    Client keys are used by external clients to authenticate with the proxy server.
    They control access to the proxy functionality.
    """
    
    key: str = Field(..., description="The client API key", min_length=1)
    enabled: bool = Field(True, description="Whether the key is currently enabled")
    description: str = Field("", description="Description of the key or its purpose")
    created_at: datetime = Field(
        default_factory=datetime.now, description="When the key was created"
    )
    last_used_at: Optional[datetime] = Field(
        None, description="When the key was last used"
    )
    usage_count: int = Field(0, ge=0, description="Total number of times the key was used")
    rate_limit: Optional[int] = Field(
        None, ge=1, description="Optional rate limit (requests per minute)"
    )
    allowed_endpoints: List[str] = Field(
        default_factory=list, description="List of allowed endpoints (empty = all allowed)"
    )
    
    @field_validator('key')
    @classmethod
    def validate_key_format(cls, v):
        """Validate that the key has a reasonable format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Key cannot be empty")
        return v.strip()
    
    @property
    def masked_key(self) -> str:
        """Get a masked version of the key for logging/display."""
        if len(self.key) <= 4:
            return "****"
        return f"...{self.key[-4:]}"
    
    def mark_used(self) -> None:
        """Mark the key as used."""
        self.usage_count += 1
        self.last_used_at = datetime.now()
    
    def is_endpoint_allowed(self, endpoint: str) -> bool:
        """Check if the key is allowed to access a specific endpoint."""
        if not self.allowed_endpoints:
            return True  # Empty list means all endpoints are allowed
        return endpoint in self.allowed_endpoints


class KeysConfig(BaseConfigModel):
    """
    Configuration model containing all keys and related settings.
    
    This is the main configuration object that holds all upstream and client keys,
    along with global settings for key management.
    """
    
    upstream_keys: List[UpstreamKey] = Field(
        default_factory=list, description="List of upstream API keys"
    )
    client_keys: List[ClientKey] = Field(
        default_factory=list, description="List of client API keys"
    )
    version: str = Field("1.0", description="Configuration version")
    created_at: datetime = Field(
        default_factory=datetime.now, description="When the configuration was created"
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, description="When the configuration was last updated"
    )
    
    def get_upstream_key_by_suffix(self, suffix: str) -> Optional[UpstreamKey]:
        """Find an upstream key by its suffix (last 4 characters)."""
        for key in self.upstream_keys:
            if key.key.endswith(suffix):
                return key
        return None
    
    def get_client_key_by_value(self, key_value: str) -> Optional[ClientKey]:
        """Find a client key by its value."""
        for key in self.client_keys:
            if key.key == key_value:
                return key
        return None
    
    def get_active_upstream_keys(self) -> List[UpstreamKey]:
        """Get all active (enabled and not permanently disabled) upstream keys."""
        return [
            key for key in self.upstream_keys
            if key.enabled and not key.permanently_disabled
        ]
    
    def get_enabled_client_keys(self) -> List[ClientKey]:
        """Get all enabled client keys."""
        return [key for key in self.client_keys if key.enabled]
    
    def update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.now()


class KeyStatistics(BaseConfigModel):
    """Statistics about key usage and status."""
    
    total_upstream_keys: int = Field(..., description="Total number of upstream keys")
    active_upstream_keys: int = Field(..., description="Number of active upstream keys")
    temporarily_disabled_keys: int = Field(..., description="Number of temporarily disabled keys")
    permanently_disabled_keys: int = Field(..., description="Number of permanently disabled keys")
    total_client_keys: int = Field(..., description="Total number of client keys")
    enabled_client_keys: int = Field(..., description="Number of enabled client keys")
    total_requests: int = Field(..., description="Total requests across all keys")
    total_failures: int = Field(..., description="Total failures across all keys")
    
    @classmethod
    def from_config(cls, config: KeysConfig) -> "KeyStatistics":
        """Create statistics from a KeysConfig object."""
        upstream_keys = config.upstream_keys
        client_keys = config.client_keys
        
        active_count = sum(1 for k in upstream_keys if k.enabled and not k.permanently_disabled)
        temp_disabled_count = sum(1 for k in upstream_keys if not k.enabled and not k.permanently_disabled)
        perm_disabled_count = sum(1 for k in upstream_keys if k.permanently_disabled)
        enabled_client_count = sum(1 for k in client_keys if k.enabled)
        total_requests = sum(k.total_requests for k in upstream_keys)
        total_failures = sum(k.failed_count for k in upstream_keys)
        
        return cls(
            total_upstream_keys=len(upstream_keys),
            active_upstream_keys=active_count,
            temporarily_disabled_keys=temp_disabled_count,
            permanently_disabled_keys=perm_disabled_count,
            total_client_keys=len(client_keys),
            enabled_client_keys=enabled_client_count,
            total_requests=total_requests,
            total_failures=total_failures
        )
