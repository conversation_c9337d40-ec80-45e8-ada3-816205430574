# AMP 0.3 Docker Deployment Guide

This guide provides instructions for deploying the Anthropic API Proxy (AMP) using Docker and Docker Compose.

## 🚀 Quick Start

### Prerequisites

- Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- Docker Compose
- At least 1GB of available RAM
- Internet connection for downloading dependencies

### 1. <PERSON><PERSON> and Prepare

```bash
git clone <your-repo-url>
cd amp0.3
```

### 2. Configure Keys

Edit `keys.json` and `models.json` with your actual API keys and model configurations:

```json
{
  "upstream_keys": [
    {
      "key": "your-actual-api-key-here",
      "weight": 1,
      "enabled": true,
      "description": "Production key 1"
    }
  ],
  "client_keys": [
    {
      "key": "your-client-access-key",
      "enabled": true,
      "description": "Production client key"
    }
  ]
}
```

### 3. Deploy

**Windows:**
```cmd
docker-start.bat
```

**Linux/Mac:**
```bash
chmod +x docker-start.sh
./docker-start.sh
```

**Manual Docker Compose:**
```bash
docker-compose up -d
```

## 📋 Configuration

### Environment Variables

The following environment variables can be configured in `docker-compose.yml`:

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server bind address |
| `PORT` | `8000` | Server port |
| `DEBUG` | `false` | Enable debug mode |
| `LOG_LEVEL` | `INFO` | Logging level |
| `TARGET_API_URL` | Required | Anthropic API endpoint |
| `MIN_ACTIVE_KEYS` | `5` | Minimum active keys to maintain |
| `ENABLE_AUTO_REGISTRATION` | `true` | Enable automatic key registration |
| `AUTO_REGISTRATION_HEADLESS` | `false` | Run registration browser in headless mode |

### Volume Mounts

- `./data:/app/data` - Configuration files (keys.json, models.json)
- `./logs:/app/logs` - Application logs
- Auto-generated: `registered_accounts.json` for auto-registered accounts

## 🔧 Management Commands

### Using Scripts

**Windows:**
```cmd
docker-start.bat start    # Start the application
docker-start.bat stop     # Stop the application
docker-start.bat restart  # Restart the application
docker-start.bat logs     # View logs
docker-start.bat status   # Show status
```

**Linux/Mac:**
```bash
./docker-start.sh start    # Start the application
./docker-start.sh stop     # Stop the application
./docker-start.sh restart  # Restart the application
./docker-start.sh logs     # View logs
./docker-start.sh status   # Show status
```

### Using Docker Compose Directly

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Restart services
docker-compose restart

# Update and restart
docker-compose pull && docker-compose up -d

# Scale services (if needed)
docker-compose up -d --scale amp-proxy=2
```

## 📊 Monitoring

### Health Checks

- **Basic Health**: `http://localhost:8000/`
- **Detailed Health**: `http://localhost:8000/health`
- **Service Status**: `http://localhost:8000/status`

### Admin Endpoints

- **System Statistics**: `http://localhost:8000/admin/stats`
- **Key Status**: `http://localhost:8000/admin/keys/status`
- **Auto Registration Config**: `http://localhost:8000/admin/keys/auto-registration/config`

### Logs

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f amp-proxy

# View last 100 lines
docker-compose logs --tail=100 amp-proxy
```

## 🔒 Security Considerations

### Production Deployment

1. **Change Default Keys**: Replace all example keys with actual production keys
2. **Network Security**: Use reverse proxy (nginx/traefik) with SSL/TLS
3. **Firewall**: Restrict access to necessary ports only
4. **Resource Limits**: Configure appropriate CPU and memory limits
5. **Log Management**: Set up log rotation and centralized logging

### Example Nginx Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Change port in docker-compose.yml
   ports:
     - "8001:8000"  # Use port 8001 instead
   ```

2. **Permission Denied**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER ./data ./logs
   ```

3. **Auto Registration Fails**
   - Check if Playwright browser is properly installed
   - Verify network connectivity
   - Check browser headless mode setting

4. **Memory Issues**
   ```bash
   # Increase memory limits in docker-compose.yml
   deploy:
     resources:
       limits:
         memory: 2G
   ```

### Debug Mode

Enable debug mode for detailed logging:

```yaml
environment:
  - DEBUG=true
  - LOG_LEVEL=DEBUG
```

### Container Shell Access

```bash
# Access running container
docker-compose exec amp-proxy bash

# Run one-off commands
docker-compose run --rm amp-proxy python -c "import sys; print(sys.version)"
```

## 📈 Performance Tuning

### Resource Optimization

1. **CPU Limits**: Adjust based on expected load
2. **Memory Limits**: Monitor usage and adjust accordingly
3. **Connection Pooling**: Configure HTTP client pool sizes
4. **Caching**: Implement response caching if needed

### Scaling

```bash
# Horizontal scaling
docker-compose up -d --scale amp-proxy=3

# Load balancer configuration needed for multiple instances
```

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose build --no-cache
docker-compose up -d
```

### Backup and Restore

```bash
# Backup configuration
tar -czf amp-backup-$(date +%Y%m%d).tar.gz data/ logs/

# Restore configuration
tar -xzf amp-backup-YYYYMMDD.tar.gz
```

## 📞 Support

For issues and support:

1. Check the logs: `docker-compose logs -f`
2. Verify configuration files
3. Check system resources
4. Review this documentation
5. Create an issue in the project repository

---

**Note**: This deployment guide assumes you have properly configured your API keys and understand the security implications of running the service in your environment.
