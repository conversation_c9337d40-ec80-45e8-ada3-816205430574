x-defaults: &defaults
  restart: unless-stopped
  labels:
    - "com.centurylinklabs.watchtower.enable=true"

services:
  amp-proxy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: amp-proxy
    depends_on:
      - cf5s
      - warp
    environment:
      # Server Configuration
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - LOG_LEVEL=INFO

      # API Configuration
      - TARGET_API_URL=https://ampcode.com/api/provider/anthropic/v1/messages
      - ANTHROPIC_VERSION=2023-06-01

      # File Paths (using mounted volumes)
      - KEYS_CONFIG_FILE=data/keys.json
      - MODELS_CONFIG_FILE=data/models.json
      - LOG_FILE=logs/amp.log

      # Retry & Timeout Settings
      - MAX_RETRIES=3
      - REQUEST_TIMEOUT=120
      - CONNECT_TIMEOUT=10
      - RETRY_DELAY=1.0

      # Health Monitoring
      - HEALTH_CHECK_INTERVAL=300
      - FAILURE_THRESHOLD=3
      - RECOVERY_TIME=300

      # Load Balancing
      - DEFAULT_LOAD_BALANCER_STRATEGY=round_robin

      # Auto Registration Settings
      - ENABLE_AUTO_REGISTRATION=true
      - MIN_ACTIVE_KEYS=5
      - MAX_REGISTRATION_ATTEMPTS=5
      - AUTO_REGISTRATION_HEADLESS=true

      # CF5S and Proxy Configuration
      - CF5S_GATEWAY_URL=http://cf5s:8080
      - PROXY_URL=socks5://warp:1080
      - USE_CF5S_BYPASS=true

      # Security
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1

    ports:
      - "8000:8000"

    volumes:
      # Persistent data storage
      - ./data:/app/data
      - ./logs:/app/logs

    networks:
      - amp-network

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

    # Resource limits
    mem_limit: 1g

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    <<: *defaults

  cf5s:
    image: dairoot/cf5s:latest
    container_name: amp-cf5s
    depends_on:
      - warp
    environment:
      - PROXY_URL=socks5://warp:1080
      - CF5S_PORT=8080
    networks:
      - amp-network
    <<: *defaults

  warp:
    image: caomingjun/warp
    container_name: amp-warp
    devices:
      - /dev/net/tun:/dev/net/tun
    ports:
      - "1080:1080"
    environment:
      - WARP_SLEEP=2
    cap_add:
      - MKNOD
      - AUDIT_WRITE
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv4.conf.all.src_valid_mark=1
    volumes:
      - ./warp-data:/var/lib/cloudflare-warp
    networks:
      - amp-network
    <<: *defaults

  watchtower:
    image: containrrr/watchtower
    container_name: amp-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: --cleanup --interval 3600 --label-enable
    networks:
      - amp-network

networks:
  amp-network:
    driver: bridge
