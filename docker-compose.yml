services:
  amp-proxy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: amp-proxy
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Server Configuration
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - LOG_LEVEL=INFO

      # API Configuration
      - TARGET_API_URL=https://ampcode.com/api/provider/anthropic/v1/messages
      - ANTHROPIC_VERSION=2023-06-01

      # File Paths (using mounted volumes)
      - KEYS_CONFIG_FILE=data/keys.json
      - MODELS_CONFIG_FILE=data/models.json
      - LOG_FILE=logs/amp.log

      # Retry & Timeout Settings
      - MAX_RETRIES=3
      - REQUEST_TIMEOUT=120
      - CONNECT_TIMEOUT=10
      - RETRY_DELAY=1.0

      # Health Monitoring
      - HEALTH_CHECK_INTERVAL=300
      - FAILURE_THRESHOLD=3
      - RECOVERY_TIME=300

      # Load Balancing
      - DEFAULT_LOAD_BALANCER_STRATEGY=round_robin

      # Auto Registration Settings
      - ENABLE_AUTO_REGISTRATION=true
      - MIN_ACTIVE_KEYS=5
      - MAX_REGISTRATION_ATTEMPTS=5
      - AUTO_REGISTRATION_HEADLESS=false

      # Security
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1

    volumes:
      # Persistent data storage
      - ./data:/app/data
      - ./logs:/app/logs

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

    # Resource limits
    mem_limit: 1g

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
