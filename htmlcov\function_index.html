<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">43%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-26 15:02 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t37">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t37"><data value='load_json_file'>ConfigLoader.load_json_file</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t66">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t66"><data value='save_json_file'>ConfigLoader.save_json_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t90">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t90"><data value='load_model_from_file'>ConfigLoader.load_model_from_file</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t116">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t116"><data value='save_model_to_file'>ConfigLoader.save_model_to_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t140">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t140"><data value='load'>KeysConfigLoader.load</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t160">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t160"><data value='save'>KeysConfigLoader.save</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t175">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t175"><data value='create_default'>KeysConfigLoader.create_default</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t192">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t192"><data value='load'>ModelsConfigLoader.load</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t230">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t230"><data value='save'>ModelsConfigLoader.save</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t245">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t245"><data value='get_default_models'>ModelsConfigLoader.get_default_models</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t277">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t277"><data value='load_keys_config'>load_keys_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t282">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t282"><data value='save_keys_config'>save_keys_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t287">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t287"><data value='load_models_config'>load_models_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t292">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t292"><data value='save_models_config'>save_models_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t109">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t109"><data value='validate_log_level'>AppSettings.validate_log_level</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t119">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t119"><data value='validate_load_balancer_strategy'>AppSettings.validate_load_balancer_strategy</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t127">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t127"><data value='base_dir'>AppSettings.base_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t132">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t132"><data value='keys_config_path'>AppSettings.keys_config_path</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t139">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t139"><data value='models_config_path'>AppSettings.models_config_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t146">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t146"><data value='log_file_path'>AppSettings.log_file_path</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>64</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="64 64">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205___init___py.html">src\key_manager\__init__.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t31">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t31"><data value='init__'>HealthMonitor.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t41">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t41"><data value='start_monitoring'>HealthMonitor.start_monitoring</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t60">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t60"><data value='stop_monitoring'>HealthMonitor.stop_monitoring</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t64">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t64"><data value='perform_health_check'>HealthMonitor.perform_health_check</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t88">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t88"><data value='check_key_health'>HealthMonitor.check_key_health</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t117">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t117"><data value='attempt_key_recovery'>HealthMonitor.attempt_key_recovery</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t146">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t146"><data value='get_health_summary'>HealthMonitor.get_health_summary</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t179">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t179"><data value='get_key_health_details'>HealthMonitor.get_key_health_details</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t225">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t225"><data value='force_recovery_check'>HealthMonitor.force_recovery_check</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="17 18">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t34">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t34"><data value='init__'>KeyManager.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t46">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t46"><data value='start'>KeyManager.start</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t54">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t54"><data value='stop'>KeyManager.stop</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t65">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t65"><data value='reload_config'>KeyManager.reload_config</data></a></td>
                <td>7</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="3 7">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t79">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t79"><data value='save_config'>KeyManager.save_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t90">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t90"><data value='config'>KeyManager.config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t96">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t96"><data value='get_next_upstream_key'>KeyManager.get_next_upstream_key</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t124">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t124"><data value='mark_key_failed'>KeyManager.mark_key_failed</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t161">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t161"><data value='verify_client_key'>KeyManager.verify_client_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t180">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t180"><data value='get_upstream_key_by_suffix'>KeyManager.get_upstream_key_by_suffix</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t192">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t192"><data value='get_client_key_by_value'>KeyManager.get_client_key_by_value</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t204">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t204"><data value='get_statistics'>KeyManager.get_statistics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t213">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t213"><data value='reset_key_status'>KeyManager.reset_key_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t237">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t237"><data value='disable_key'>KeyManager.disable_key</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t257">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t257"><data value='enable_key'>KeyManager.enable_key</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t280">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t280"><data value='get_load_balancer_strategy'>KeyManager.get_load_balancer_strategy</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t289">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t289"><data value='set_load_balancer_strategy'>KeyManager.set_load_balancer_strategy</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t311">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t311"><data value='get_available_strategies'>KeyManager.get_available_strategies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc___init___py.html">src\load_balancer\__init__.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t23">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t23"><data value='select_key'>LoadBalancerStrategy.select_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t41">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t41"><data value='name'>LoadBalancerStrategy.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t46">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t46"><data value='description'>LoadBalancerStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t50">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t50"><data value='filter_available_keys'>LoadBalancerStrategy.filter_available_keys</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t73">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t73"><data value='validate_keys'>LoadBalancerStrategy.validate_keys</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t98">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t98"><data value='init__'>StatefulStrategy.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t102">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t102"><data value='reset_state'>StatefulStrategy.reset_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t106">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t106"><data value='get_state'>StatefulStrategy.get_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t110">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t110"><data value='set_state'>StatefulStrategy.set_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t123">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t123"><data value='init__'>IndexBasedStrategy.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t129">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t129"><data value='current_index'>IndexBasedStrategy.current_index</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t134">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t134"><data value='current_index'>IndexBasedStrategy.current_index</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t138">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t138"><data value='advance_index'>IndexBasedStrategy.advance_index</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t151">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t151"><data value='get_current_and_advance'>IndexBasedStrategy.get_current_and_advance</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t34">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t34"><data value='init__'>StrategyManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t42">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t42"><data value='register_default_strategies'>StrategyManager._register_default_strategies</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t56">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t56"><data value='register_strategy'>StrategyManager.register_strategy</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t66">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t66"><data value='unregister_strategy'>StrategyManager.unregister_strategy</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t85">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t85"><data value='get_strategy'>StrategyManager.get_strategy</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t103">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t103"><data value='list_strategies'>StrategyManager.list_strategies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t112">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t112"><data value='get_strategy_info'>StrategyManager.get_strategy_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t133">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t133"><data value='set_current_strategy'>StrategyManager.set_current_strategy</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t151">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t151"><data value='get_current_strategy'>StrategyManager.get_current_strategy</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t160">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t160"><data value='get_current_strategy_name'>StrategyManager.get_current_strategy_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t169">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t169"><data value='select_key'>StrategyManager.select_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t196">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t196"><data value='reset_strategy_state'>StrategyManager.reset_strategy_state</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t212">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t212"><data value='get_strategy_state'>StrategyManager.get_strategy_state</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t232">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t232"><data value='validate_strategy_name'>StrategyManager.validate_strategy_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t24">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t24"><data value='name'>RoundRobinStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t29">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t29"><data value='description'>RoundRobinStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t33">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t33"><data value='select_key'>RoundRobinStrategy.select_key</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t63">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t63"><data value='name'>WeightedRoundRobinStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t68">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t68"><data value='description'>WeightedRoundRobinStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t72">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t72"><data value='select_key'>WeightedRoundRobinStrategy.select_key</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t110">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t110"><data value='name'>RandomStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t115">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t115"><data value='description'>RandomStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t119">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t119"><data value='select_key'>RandomStrategy.select_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t148">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t148"><data value='name'>WeightedRandomStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t153">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t153"><data value='description'>WeightedRandomStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t157">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t157"><data value='select_key'>WeightedRandomStrategy.select_key</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t201">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t201"><data value='name'>LeastUsedStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t206">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t206"><data value='description'>LeastUsedStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t210">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t210"><data value='select_key'>LeastUsedStrategy.select_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t239">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t239"><data value='name'>LeastFailedStrategy.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t244">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t244"><data value='description'>LeastFailedStrategy.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t248">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t248"><data value='select_key'>LeastFailedStrategy.select_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t35">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t35"><data value='lifespan'>lifespan</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t90">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t90"><data value='create_app'>create_app</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t128">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t128"><data value='setup_exception_handlers'>setup_exception_handlers</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t137">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t137"><data value='http_exception_handler'>setup_exception_handlers.http_exception_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t156">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t156"><data value='general_exception_handler'>setup_exception_handlers.general_exception_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t174">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t174"><data value='timeout_exception_handler'>setup_exception_handlers.timeout_exception_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t196">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t196"><data value='main'>main</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="25 26">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3___init___py.html">src\models\__init__.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t25">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t25"><data value='validate_model_id'>AnthropicModel.validate_model_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t54">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t54"><data value='validate_role'>Message.validate_role</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t76">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t76"><data value='validate_messages_not_empty'>MessagesRequest.validate_messages_not_empty</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t84">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t84"><data value='validate_model_not_empty'>MessagesRequest.validate_model_not_empty</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t100">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t100"><data value='validate_total_tokens'>Usage.validate_total_tokens</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t144">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t144"><data value='masked_client_key'>ProxyRequest.masked_client_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t151">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t151"><data value='masked_upstream_key'>ProxyRequest.masked_upstream_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t169">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t169"><data value='masked_upstream_key'>ProxyResponse.masked_upstream_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t185">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t185"><data value='from_exception'>ErrorResponse.from_exception</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>86</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="86 86">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t72">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t72"><data value='offset'>PaginationParams.offset</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t88">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t88"><data value='calculate_total_pages'>PaginatedResponse.calculate_total_pages</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t48">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t48"><data value='validate_key_format'>UpstreamKey.validate_key_format</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t55">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t55"><data value='status'>UpstreamKey.status</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t65">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t65"><data value='masked_key'>UpstreamKey.masked_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t71">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t71"><data value='mark_used'>UpstreamKey.mark_used</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t76">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t76"><data value='mark_failed'>UpstreamKey.mark_failed</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t81">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t81"><data value='reset_failures'>UpstreamKey.reset_failures</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t87">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t87"><data value='disable_temporarily'>UpstreamKey.disable_temporarily</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t91">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t91"><data value='disable_permanently'>UpstreamKey.disable_permanently</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t96">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t96"><data value='enable'>UpstreamKey.enable</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t129">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t129"><data value='validate_key_format'>ClientKey.validate_key_format</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t136">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t136"><data value='masked_key'>ClientKey.masked_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t142">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t142"><data value='mark_used'>ClientKey.mark_used</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t147">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t147"><data value='is_endpoint_allowed'>ClientKey.is_endpoint_allowed</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t176">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t176"><data value='get_upstream_key_by_suffix'>KeysConfig.get_upstream_key_by_suffix</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t183">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t183"><data value='get_client_key_by_value'>KeysConfig.get_client_key_by_value</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t190">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t190"><data value='get_active_upstream_keys'>KeysConfig.get_active_upstream_keys</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t197">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t197"><data value='get_enabled_client_keys'>KeysConfig.get_enabled_client_keys</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t201">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t201"><data value='update_timestamp'>KeysConfig.update_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t219">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t219"><data value='from_config'>KeyStatistics.from_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>67</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="67 67">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833___init___py.html">src\routers\__init__.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t41">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t41"><data value='set_load_balancer_strategy'>set_load_balancer_strategy</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t76">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t76"><data value='list_strategies'>list_strategies</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t107">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t107"><data value='get_statistics'>get_statistics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t165">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t165"><data value='get_keys_status'>get_keys_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t217">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t217"><data value='reset_key_status'>reset_key_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t254">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t254"><data value='disable_key'>disable_key</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t291">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t291"><data value='enable_key'>enable_key</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t334">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t334"><data value='get_detailed_health'>get_detailed_health</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t377">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t377"><data value='force_health_check'>force_health_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t45">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t45"><data value='set_http_client'>set_http_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t51">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t51"><data value='stream_response_generator'>stream_response_generator</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t129">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t129"><data value='forward_request'>forward_request</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t219">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t219"><data value='list_models'>list_models</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t265">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html#t265"><data value='proxy_messages_request'>proxy_messages_request</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t19">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t19"><data value='verify_client_api_key'>verify_client_api_key</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t68">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t68"><data value='get_optional_client_key'>get_optional_client_key</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t91">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t91"><data value='get_request_info'>get_request_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t119">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t119"><data value='init__'>RateLimiter.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t129">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t129"><data value='call__'>RateLimiter.__call__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t149">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t149"><data value='validate_json_request'>validate_json_request</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t184">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t184"><data value='get_anthropic_headers'>get_anthropic_headers</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t209">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t209"><data value='log_request_start'>log_request_start</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t238">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t238"><data value='init__'>RequestContext.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t246">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t246"><data value='set_client_key'>RequestContext.set_client_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t250">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t250"><data value='set_upstream_key'>RequestContext.set_upstream_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t254">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t254"><data value='set_streaming'>RequestContext.set_streaming</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t259">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t259"><data value='get_request_context'>get_request_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t26">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t26"><data value='root_health_check'>root_health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t78">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t78"><data value='detailed_health_check'>detailed_health_check</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t160">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t160"><data value='service_status'>service_status</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t197">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t197"><data value='ping'>ping</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t208">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t208"><data value='get_version'>get_version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t223">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html#t223"><data value='get_metrics'>get_metrics</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t12">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t12"><data value='mask_key'>mask_key</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t41">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t41"><data value='validate_key_format'>validate_key_format</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t79">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t79"><data value='normalize_key'>normalize_key</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t114">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t114"><data value='extract_key_suffix'>extract_key_suffix</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t139">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t139"><data value='is_key_similar'>is_key_similar</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t166">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t166"><data value='generate_key_id'>generate_key_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t189">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t189"><data value='sanitize_key_for_logging'>sanitize_key_for_logging</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t217">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t217"><data value='validate_client_key_format'>validate_client_key_format</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t240">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t240"><data value='validate_upstream_key_format'>validate_upstream_key_format</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t267">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html#t267"><data value='compare_key_suffixes'>compare_key_suffixes</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t17">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t17"><data value='setup_logging'>setup_logging</data></a></td>
                <td>22</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="14 22">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t78">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t78"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t91">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t91"><data value='log_request_start'>log_request_start</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t121">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t121"><data value='log_request_end'>log_request_end</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t152">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t152"><data value='log_key_event'>log_key_event</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t175">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t175"><data value='log_strategy_change'>log_strategy_change</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t191">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t191"><data value='log_error_with_context'>log_error_with_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t216">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t216"><data value='log_health_check'>log_health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t239">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t239"><data value='log_configuration_loaded'>log_configuration_loaded</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t258">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t258"><data value='log_configuration_saved'>log_configuration_saved</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t285">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t285"><data value='init__'>ContextualLogger.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t296">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t296"><data value='format_message'>ContextualLogger._format_message</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t305">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t305"><data value='debug'>ContextualLogger.debug</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t309">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t309"><data value='info'>ContextualLogger.info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t313">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t313"><data value='warning'>ContextualLogger.warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t317">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t317"><data value='error'>ContextualLogger.error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t321">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t321"><data value='critical'>ContextualLogger.critical</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t13">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t13"><data value='get_current_timestamp'>get_current_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t28">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t28"><data value='get_current_datetime'>get_current_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t43">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t43"><data value='timestamp_to_datetime'>timestamp_to_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t61">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t61"><data value='datetime_to_timestamp'>datetime_to_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t80">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t80"><data value='format_datetime'>format_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t99">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t99"><data value='parse_datetime'>parse_datetime</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t119">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t119"><data value='time_since'>time_since</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t138">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t138"><data value='time_until'>time_until</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t157">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t157"><data value='is_expired'>is_expired</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t180">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t180"><data value='add_seconds'>add_seconds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t200">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t200"><data value='subtract_seconds'>subtract_seconds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t220">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t220"><data value='format_duration'>format_duration</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t256">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t256"><data value='format_relative_time'>format_relative_time</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t297">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t297"><data value='sleep_until'>sleep_until</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t328">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t328"><data value='init__'>Timer.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t333">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t333"><data value='start'>Timer.start</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t338">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t338"><data value='stop'>Timer.stop</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t351">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t351"><data value='elapsed'>Timer.elapsed</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t364">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t364"><data value='reset'>Timer.reset</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t370">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t370"><data value='is_running'>Timer.is_running</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t15">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t15"><data value='validate_url'>validate_url</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t43">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t43"><data value='validate_http_method'>validate_http_method</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t72">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t72"><data value='validate_content_type'>validate_content_type</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t98">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t98"><data value='validate_json_structure'>validate_json_structure</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t121">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t121"><data value='validate_anthropic_request'>validate_anthropic_request</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t192">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t192"><data value='validate_model_id'>validate_model_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t224">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t224"><data value='validate_port_number'>validate_port_number</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t251">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t251"><data value='validate_timeout_value'>validate_timeout_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t278">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t278"><data value='validate_weight_value'>validate_weight_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t305">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t305"><data value='validate_rate_limit'>validate_rate_limit</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t332">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html#t332"><data value='sanitize_string'>sanitize_string</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1738</td>
                <td>986</td>
                <td>0</td>
                <td class="right" data-ratio="752 1738">43%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-26 15:02 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
