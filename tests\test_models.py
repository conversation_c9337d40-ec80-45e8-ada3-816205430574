"""
Tests for data models.

This module contains unit tests for all data models including
keys, API models, and base models.
"""

import pytest
from datetime import datetime, timezone
from pydantic import ValidationError

from src.models import (
    AnthropicModel,
    ClientKey,
    KeyStatistics,
    KeysConfig,
    KeyStatus,
    LoadBalancerStrategy,
    Message,
    MessagesRequest,
    UpstreamKey,
    Usage,
)


class TestUpstreamKey:
    """Test cases for UpstreamKey model."""
    
    def test_create_upstream_key(self):
        """Test creating a valid upstream key."""
        key = UpstreamKey(
            key="test_key_abcd",
            weight=2,
            description="Test key"
        )
        
        assert key.key == "test_key_abcd"
        assert key.weight == 2
        assert key.enabled is True
        assert key.failed_count == 0
        assert key.total_requests == 0
        assert key.recovery_attempted is False
        assert key.permanently_disabled is False
        assert key.description == "Test key"
        assert isinstance(key.created_at, datetime)
    
    def test_upstream_key_validation(self):
        """Test upstream key validation."""
        # Empty key should raise validation error
        with pytest.raises(ValidationError):
            UpstreamKey(key="")
        
        # Whitespace-only key should be stripped and validated
        with pytest.raises(ValidationError):
            UpstreamKey(key="   ")
    
    def test_upstream_key_status_property(self):
        """Test the status property of upstream key."""
        # Active key
        key = UpstreamKey(key="test_key")
        assert key.status == KeyStatus.ACTIVE
        
        # Temporarily disabled key
        key.enabled = False
        assert key.status == KeyStatus.TEMPORARILY_DISABLED
        
        # Permanently disabled key
        key.permanently_disabled = True
        assert key.status == KeyStatus.PERMANENTLY_DISABLED
    
    def test_upstream_key_masked_key_property(self):
        """Test the masked_key property."""
        key = UpstreamKey(key="test_key_abcd")
        assert key.masked_key == "...abcd"
        
        # Short key
        short_key = UpstreamKey(key="abc")
        assert short_key.masked_key == "****"
    
    def test_mark_used(self):
        """Test marking key as used."""
        key = UpstreamKey(key="test_key")
        initial_requests = key.total_requests
        
        key.mark_used()
        
        assert key.total_requests == initial_requests + 1
        assert isinstance(key.last_used_at, datetime)
    
    def test_mark_failed(self):
        """Test marking key as failed."""
        key = UpstreamKey(key="test_key")
        initial_failures = key.failed_count
        
        key.mark_failed()
        
        assert key.failed_count == initial_failures + 1
        assert isinstance(key.last_failed_time, datetime)
    
    def test_reset_failures(self):
        """Test resetting failure-related fields."""
        key = UpstreamKey(key="test_key")
        key.mark_failed()
        key.recovery_attempted = True
        
        key.reset_failures()
        
        assert key.failed_count == 0
        assert key.last_failed_time is None
        assert key.recovery_attempted is False
    
    def test_disable_temporarily(self):
        """Test temporarily disabling a key."""
        key = UpstreamKey(key="test_key")
        key.disable_temporarily()
        
        assert key.enabled is False
        assert key.permanently_disabled is False
    
    def test_disable_permanently(self):
        """Test permanently disabling a key."""
        key = UpstreamKey(key="test_key")
        key.disable_permanently()
        
        assert key.enabled is False
        assert key.permanently_disabled is True
    
    def test_enable(self):
        """Test enabling a key."""
        key = UpstreamKey(key="test_key")
        key.enabled = False
        
        key.enable()
        assert key.enabled is True
        
        # Cannot enable permanently disabled key
        key.permanently_disabled = True
        key.enabled = False
        
        key.enable()
        assert key.enabled is False


class TestClientKey:
    """Test cases for ClientKey model."""
    
    def test_create_client_key(self):
        """Test creating a valid client key."""
        key = ClientKey(
            key="client_test_key",
            description="Test client key"
        )
        
        assert key.key == "client_test_key"
        assert key.enabled is True
        assert key.description == "Test client key"
        assert key.usage_count == 0
        assert isinstance(key.created_at, datetime)
    
    def test_client_key_validation(self):
        """Test client key validation."""
        with pytest.raises(ValidationError):
            ClientKey(key="")
    
    def test_client_key_masked_key_property(self):
        """Test the masked_key property."""
        key = ClientKey(key="client_test_key")
        assert key.masked_key == "...t_key"
    
    def test_mark_used(self):
        """Test marking client key as used."""
        key = ClientKey(key="client_test_key")
        initial_count = key.usage_count
        
        key.mark_used()
        
        assert key.usage_count == initial_count + 1
        assert isinstance(key.last_used_at, datetime)
    
    def test_is_endpoint_allowed(self):
        """Test endpoint access control."""
        # No restrictions (empty list)
        key = ClientKey(key="client_test_key")
        assert key.is_endpoint_allowed("/any/endpoint") is True
        
        # With restrictions
        key.allowed_endpoints = ["/v1/messages", "/v1/models"]
        assert key.is_endpoint_allowed("/v1/messages") is True
        assert key.is_endpoint_allowed("/v1/models") is True
        assert key.is_endpoint_allowed("/admin/stats") is False


class TestKeysConfig:
    """Test cases for KeysConfig model."""
    
    def test_create_keys_config(self, sample_upstream_keys, sample_client_keys):
        """Test creating a keys configuration."""
        config = KeysConfig(
            upstream_keys=sample_upstream_keys,
            client_keys=sample_client_keys
        )
        
        assert len(config.upstream_keys) == 3
        assert len(config.client_keys) == 3
        assert isinstance(config.created_at, datetime)
        assert isinstance(config.updated_at, datetime)
    
    def test_get_upstream_key_by_suffix(self, sample_keys_config):
        """Test finding upstream key by suffix."""
        key = sample_keys_config.get_upstream_key_by_suffix("abcd")
        assert key is not None
        assert key.key == "test_key_1_abcd"
        
        # Non-existent suffix
        key = sample_keys_config.get_upstream_key_by_suffix("xyz")
        assert key is None
    
    def test_get_client_key_by_value(self, sample_keys_config):
        """Test finding client key by value."""
        key = sample_keys_config.get_client_key_by_value("client_key_1")
        assert key is not None
        assert key.key == "client_key_1"
        
        # Non-existent key
        key = sample_keys_config.get_client_key_by_value("non_existent")
        assert key is None
    
    def test_get_active_upstream_keys(self, sample_keys_config):
        """Test getting active upstream keys."""
        active_keys = sample_keys_config.get_active_upstream_keys()
        assert len(active_keys) == 2  # Only enabled and not permanently disabled
        
        for key in active_keys:
            assert key.enabled is True
            assert key.permanently_disabled is False
    
    def test_get_enabled_client_keys(self, sample_keys_config):
        """Test getting enabled client keys."""
        enabled_keys = sample_keys_config.get_enabled_client_keys()
        assert len(enabled_keys) == 2  # Only enabled keys
        
        for key in enabled_keys:
            assert key.enabled is True
    
    def test_update_timestamp(self, sample_keys_config):
        """Test updating the timestamp."""
        original_time = sample_keys_config.updated_at
        sample_keys_config.update_timestamp()
        
        assert sample_keys_config.updated_at > original_time


class TestAnthropicModel:
    """Test cases for AnthropicModel."""
    
    def test_create_anthropic_model(self):
        """Test creating an Anthropic model."""
        model = AnthropicModel(
            id="claude-3-sonnet-20240229",
            owned_by="anthropic"
        )
        
        assert model.id == "claude-3-sonnet-20240229"
        assert model.object == "model"
        assert model.owned_by == "anthropic"
    
    def test_model_validation(self):
        """Test model validation."""
        with pytest.raises(ValidationError):
            AnthropicModel(id="", owned_by="anthropic")


class TestMessage:
    """Test cases for Message model."""
    
    def test_create_message(self):
        """Test creating a message."""
        message = Message(
            role="user",
            content="Hello, world!"
        )
        
        assert message.role == "user"
        assert message.content == "Hello, world!"
    
    def test_message_role_validation(self):
        """Test message role validation."""
        with pytest.raises(ValidationError):
            Message(role="invalid_role", content="test")


class TestMessagesRequest:
    """Test cases for MessagesRequest model."""
    
    def test_create_messages_request(self):
        """Test creating a messages request."""
        messages = [
            Message(role="user", content="Hello")
        ]
        
        request = MessagesRequest(
            model="claude-3-sonnet",
            messages=messages,
            max_tokens=100,
            temperature=0.7
        )
        
        assert request.model == "claude-3-sonnet"
        assert len(request.messages) == 1
        assert request.max_tokens == 100
        assert request.temperature == 0.7
        assert request.stream is False
    
    def test_messages_request_validation(self):
        """Test messages request validation."""
        # Empty messages
        with pytest.raises(ValidationError):
            MessagesRequest(
                model="claude-3-sonnet",
                messages=[],
                max_tokens=100
            )
        
        # Empty model
        with pytest.raises(ValidationError):
            MessagesRequest(
                model="",
                messages=[Message(role="user", content="test")],
                max_tokens=100
            )


class TestUsage:
    """Test cases for Usage model."""
    
    def test_create_usage(self):
        """Test creating usage information."""
        usage = Usage(
            input_tokens=10,
            output_tokens=15,
            total_tokens=25
        )
        
        assert usage.input_tokens == 10
        assert usage.output_tokens == 15
        assert usage.total_tokens == 25
    
    def test_usage_total_validation(self):
        """Test that total tokens is auto-corrected."""
        usage = Usage(
            input_tokens=10,
            output_tokens=15,
            total_tokens=20  # Wrong total
        )
        
        # Should be auto-corrected to 25
        assert usage.total_tokens == 25


class TestKeyStatistics:
    """Test cases for KeyStatistics model."""
    
    def test_from_config(self, sample_keys_config):
        """Test creating statistics from config."""
        stats = KeyStatistics.from_config(sample_keys_config)
        
        assert stats.total_upstream_keys == 3
        assert stats.active_upstream_keys == 2
        assert stats.temporarily_disabled_keys == 1
        assert stats.permanently_disabled_keys == 0
        assert stats.total_client_keys == 3
        assert stats.enabled_client_keys == 2
