#!/usr/bin/env python3
"""
Standalone test for the auto registration functionality.

This script tests the registration functionality independently
to ensure it works correctly before integrating with the main system.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.key_manager.auto_registrar import AutoRegistrar
from src.utils import setup_logging, get_logger

logger = get_logger(__name__)


async def test_single_registration():
    """Test a single registration attempt."""
    print("🧪 Testing Single Registration")
    print("=" * 50)
    
    # Initialize registrar with visible browser for debugging
    registrar = AutoRegistrar(
        min_keys=1,
        max_registration_attempts=1,
        headless=False  # Keep browser visible
    )
    
    print("ℹ️  Starting registration test...")
    print("ℹ️  Browser will be visible for debugging")
    print("ℹ️  This may take 2-3 minutes...")
    
    try:
        # Test single registration
        account_info = await registrar.register_single_account()
        
        if account_info:
            print("\n✅ Registration Test SUCCESSFUL!")
            print(f"   Name: {account_info['first_name']} {account_info['last_name']}")
            print(f"   Email: {account_info['email']}")
            print(f"   API Key: {account_info['api_key'][:20]}...")
            
            # Save the account info
            registrar.save_account_info(account_info)
            print(f"   Account saved to: {registrar.accounts_file}")
            
            return True
        else:
            print("\n❌ Registration Test FAILED!")
            print("   No account information returned")
            return False
            
    except Exception as e:
        print(f"\n❌ Registration Test FAILED!")
        print(f"   Error: {e}")
        return False


async def test_email_verification():
    """Test email verification functionality."""
    print("\n🧪 Testing Email Verification")
    print("=" * 50)
    
    registrar = AutoRegistrar()
    
    # Generate a test email
    test_email = registrar.generate_random_email()
    print(f"ℹ️  Generated test email: {test_email}")
    
    # Test mail list retrieval
    print("ℹ️  Testing mail list retrieval...")
    mails = registrar.get_mail_list(test_email)
    print(f"   Found {len(mails)} emails in mailbox")
    
    if mails:
        print("   Latest email subjects:")
        for i, mail in enumerate(mails[:3]):
            print(f"   {i+1}. {mail.get('subject', 'No subject')}")
    
    return True


async def test_configuration():
    """Test registrar configuration."""
    print("\n🧪 Testing Configuration")
    print("=" * 50)
    
    registrar = AutoRegistrar(min_keys=5, max_registration_attempts=3)
    
    print(f"ℹ️  Min keys: {registrar.min_keys}")
    print(f"ℹ️  Max attempts: {registrar.max_registration_attempts}")
    print(f"ℹ️  Headless mode: {registrar.headless}")
    print(f"ℹ️  Accounts file: {registrar.accounts_file}")
    print(f"ℹ️  Base URL: {registrar.base_url}")
    
    # Test threshold checking
    print(f"ℹ️  Should register with 2 active keys: {registrar.should_register_new_keys(2)}")
    print(f"ℹ️  Should register with 6 active keys: {registrar.should_register_new_keys(6)}")
    print(f"ℹ️  Keys needed with 2 active: {registrar.calculate_keys_needed(2)}")
    print(f"ℹ️  Keys needed with 6 active: {registrar.calculate_keys_needed(6)}")
    
    return True


async def main():
    """Main test function."""
    print("🚀 Auto Registration Standalone Test")
    print("=" * 60)
    
    # Setup logging
    setup_logging()
    
    # Test configuration first
    config_success = await test_configuration()
    
    # Test email functionality
    email_success = await test_email_verification()
    
    # Ask user if they want to test actual registration
    print("\n" + "=" * 60)
    print("⚠️  REGISTRATION TEST WARNING")
    print("=" * 60)
    print("The next test will attempt actual registration on ampcode.com")
    print("This will:")
    print("  • Open a browser window")
    print("  • Create a temporary email account")
    print("  • Attempt to register a new account")
    print("  • Try to extract an API key")
    print("\nThis test may take 2-3 minutes to complete.")
    
    response = input("\nDo you want to proceed with the registration test? (y/N): ").strip().lower()
    
    registration_success = True
    if response in ['y', 'yes']:
        registration_success = await test_single_registration()
    else:
        print("ℹ️  Registration test skipped by user")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Configuration Test", config_success),
        ("Email Verification Test", email_success),
        ("Registration Test", registration_success if response in ['y', 'yes'] else "Skipped")
    ]
    
    for test_name, result in tests:
        if result is True:
            print(f"✅ {test_name}: PASSED")
        elif result is False:
            print(f"❌ {test_name}: FAILED")
        else:
            print(f"⏭️  {test_name}: {result}")
    
    if all(r is True for r in [config_success, email_success]) and (response not in ['y', 'yes'] or registration_success):
        print("\n🎉 All tests completed successfully!")
        print("\nThe auto registration functionality appears to be working correctly.")
        print("You can now integrate it with the main system.")
    else:
        print("\n⚠️  Some tests failed or were skipped.")
        print("Please review the output above and fix any issues before proceeding.")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user.")
    except Exception as e:
        print(f"\n\n💥 Unexpected error during test: {e}")
        sys.exit(1)
