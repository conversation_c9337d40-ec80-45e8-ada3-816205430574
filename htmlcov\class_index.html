<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">43%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-26 15:02 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t23">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t23"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t28">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t28"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>33</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="11 33">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t136">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t136"><data value='KeysConfigLoader'>KeysConfigLoader</data></a></td>
                <td>10</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="3 10">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t188">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t188"><data value='ModelsConfigLoader'>ModelsConfigLoader</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="39 42">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t16">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t16"><data value='AppSettings'>AppSettings</data></a></td>
                <td>21</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="12 21">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t100">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t100"><data value='Config'>AppSettings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t155">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t155"><data value='DatabaseSettings'>DatabaseSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t168">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t168"><data value='Config'>DatabaseSettings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="64 64">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205___init___py.html">src\key_manager\__init__.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t21">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html#t21"><data value='HealthMonitor'>HealthMonitor</data></a></td>
                <td>86</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="2 86">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_health_py.html">src\key_manager\health.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="17 18">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t22">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html#t22"><data value='KeyManager'>KeyManager</data></a></td>
                <td>95</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="8 95">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html">src\key_manager\manager.py</a></td>
                <td class="name left"><a href="z_b4978456b28df205_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc___init___py.html">src\load_balancer\__init__.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t14">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t14"><data value='LoadBalancerStrategy'>LoadBalancerStrategy</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t90">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t90"><data value='StatefulStrategy'>StatefulStrategy</data></a></td>
                <td>4</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="1 4">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t115">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html#t115"><data value='IndexBasedStrategy'>IndexBasedStrategy</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html">src\load_balancer\base.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t26">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html#t26"><data value='StrategyManager'>StrategyManager</data></a></td>
                <td>46</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="8 46">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html">src\load_balancer\manager.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t15">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t15"><data value='RoundRobinStrategy'>RoundRobinStrategy</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t54">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t54"><data value='WeightedRoundRobinStrategy'>WeightedRoundRobinStrategy</data></a></td>
                <td>11</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="1 11">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t101">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t101"><data value='RandomStrategy'>RandomStrategy</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t139">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t139"><data value='WeightedRandomStrategy'>WeightedRandomStrategy</data></a></td>
                <td>14</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="1 14">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t192">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t192"><data value='LeastUsedStrategy'>LeastUsedStrategy</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t230">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html#t230"><data value='LeastFailedStrategy'>LeastFailedStrategy</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html">src\load_balancer\strategies.py</a></td>
                <td class="name left"><a href="z_cdc09ce95c6a37dc_strategies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="39 74">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3___init___py.html">src\models\__init__.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t15">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t15"><data value='AnthropicModel'>AnthropicModel</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t32">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t32"><data value='ModelsListResponse'>ModelsListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t39">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t39"><data value='MessageContent'>MessageContent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t46">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t46"><data value='Message'>Message</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t62">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t62"><data value='MessagesRequest'>MessagesRequest</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t91">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t91"><data value='Usage'>Usage</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t111">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t111"><data value='MessagesResponse'>MessagesResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t124">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t124"><data value='StreamChunk'>StreamChunk</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t133">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t133"><data value='ProxyRequest'>ProxyRequest</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t158">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t158"><data value='ProxyResponse'>ProxyResponse</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t176">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html#t176"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html">src\models\api.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>86</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="86 86">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t15">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t15"><data value='KeyStatus'>KeyStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t23">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t23"><data value='LoadBalancerStrategy'>LoadBalancerStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t32">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t32"><data value='BaseConfigModel'>BaseConfigModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t35">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t35"><data value='Config'>BaseConfigModel.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t46">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t46"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t55">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t55"><data value='APIResponse'>APIResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t65">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t65"><data value='PaginationParams'>PaginationParams</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t77">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html#t77"><data value='PaginatedResponse'>PaginatedResponse</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html">src\models\base.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t16">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t16"><data value='UpstreamKey'>UpstreamKey</data></a></td>
                <td>23</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="2 23">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t102">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t102"><data value='ClientKey'>ClientKey</data></a></td>
                <td>11</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="2 11">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t154">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t154"><data value='KeysConfig'>KeysConfig</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t206">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html#t206"><data value='KeyStatistics'>KeyStatistics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html">src\models\keys.py</a></td>
                <td class="name left"><a href="z_532385f02888fdd3_keys_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="67 67">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833___init___py.html">src\routers\__init__.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t25">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t25"><data value='StrategyChangeRequest'>StrategyChangeRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t30">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t30"><data value='KeyActionRequest'>KeyActionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t35">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html#t35"><data value='KeyResetRequest'>KeyResetRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html">src\routers\admin.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_admin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>124</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="34 124">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html">src\routers\core.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>125</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="22 125">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t111">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t111"><data value='RateLimiter'>RateLimiter</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t230">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html#t230"><data value='RequestContext'>RequestContext</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html">src\routers\dependencies.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="22 60">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html">src\routers\health.py</a></td>
                <td class="name left"><a href="z_9ffb13e3a994f833_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="22 75">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html">src\utils\keys.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_keys_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="12 57">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t277">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html#t277"><data value='ContextualLogger'>ContextualLogger</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html">src\utils\logging.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="39 76">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t316">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html#t316"><data value='Timer'>Timer</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_time_py.html">src\utils\time.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_time_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="25 71">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html">src\utils\validation.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>98</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="15 98">15%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1738</td>
                <td>986</td>
                <td>0</td>
                <td class="right" data-ratio="752 1738">43%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-26 15:02 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
