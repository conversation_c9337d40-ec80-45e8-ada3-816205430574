"""
Administrative API routes.

This module contains administrative endpoints for managing the proxy server,
including key management, strategy configuration, and system monitoring.
"""

import logging
from typing import Dict, List

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from ..key_manager import key_manager
from ..models import APIResponse, KeyStatistics
from ..utils import get_logger, log_strategy_change
from .dependencies import verify_client_api_key

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/admin", tags=["admin"])


class StrategyChangeRequest(BaseModel):
    """Request model for changing load balancing strategy."""
    strategy: str


class KeyActionRequest(BaseModel):
    """Request model for key actions."""
    key_suffix: str


class KeyResetRequest(BaseModel):
    """Request model for resetting key status."""
    key_suffix: str


class AutoRegistrationRequest(BaseModel):
    """Request model for triggering auto registration."""
    count: int = Field(ge=1, le=10, description="Number of keys to register")


class AutoRegistrationConfigRequest(BaseModel):
    """Request model for updating auto registration configuration."""
    enable_auto_registration: bool
    min_active_keys: int = Field(ge=1, le=10)
    max_registration_attempts: int = Field(ge=1, le=20)


@router.post("/strategy")
async def set_load_balancer_strategy(
    request: StrategyChangeRequest,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Set the load balancing strategy.
    
    Args:
        request: Strategy change request
        client_key: Validated client API key
        
    Returns:
        API response with success/failure status
    """
    try:
        old_strategy = key_manager.get_load_balancer_strategy()
        key_manager.set_load_balancer_strategy(request.strategy)
        
        log_strategy_change(logger, old_strategy, request.strategy)
        
        return APIResponse(
            success=True,
            message=f"Load balancing strategy changed to: {request.strategy}",
            data={"old_strategy": old_strategy, "new_strategy": request.strategy}
        )
        
    except ValueError as e:
        logger.warning(f"Invalid strategy change request: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error changing strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to change strategy")


@router.get("/strategies")
async def list_strategies(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    List all available load balancing strategies.
    
    Args:
        client_key: Validated client API key
        
    Returns:
        API response with list of strategies
    """
    try:
        strategies = key_manager.get_available_strategies()
        current_strategy = key_manager.get_load_balancer_strategy()
        
        return APIResponse(
            success=True,
            message="Available load balancing strategies",
            data={
                "strategies": strategies,
                "current_strategy": current_strategy
            }
        )
        
    except Exception as e:
        logger.error(f"Error listing strategies: {e}")
        raise HTTPException(status_code=500, detail="Failed to list strategies")


@router.get("/stats")
async def get_statistics(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Get comprehensive system statistics.
    
    Args:
        client_key: Validated client API key
        
    Returns:
        API response with system statistics
    """
    try:
        stats = key_manager.get_statistics()
        current_strategy = key_manager.get_load_balancer_strategy()
        
        # Get detailed key information
        config = key_manager.config
        keys_stats = []
        
        for key in config.upstream_keys:
            keys_stats.append({
                "key_suffix": key.masked_key,
                "enabled": key.enabled,
                "permanently_disabled": key.permanently_disabled,
                "recovery_attempted": key.recovery_attempted,
                "weight": key.weight,
                "total_requests": key.total_requests,
                "failed_count": key.failed_count,
                "last_failed_time": key.last_failed_time,
                "status": (
                    "permanently_disabled" if key.permanently_disabled
                    else "active" if key.enabled
                    else "temporarily_disabled"
                ),
                "description": key.description
            })
        
        return APIResponse(
            success=True,
            message="System statistics",
            data={
                "load_balancer": {
                    "strategy": current_strategy,
                    "available_strategies": key_manager.get_available_strategies()
                },
                "key_summary": stats.dict(),
                "keys_details": keys_stats,
                "models_count": len(key_manager.config.upstream_keys)  # TODO: Get actual models count
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")


@router.get("/keys/status")
async def get_keys_status(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Get detailed status of all keys.
    
    Args:
        client_key: Validated client API key
        
    Returns:
        API response with detailed key status
    """
    try:
        stats = key_manager.get_statistics()
        config = key_manager.config
        
        keys_details = []
        for key in config.upstream_keys:
            keys_details.append({
                "key_suffix": key.key[-4:] if len(key.key) >= 4 else key.key,
                "status": (
                    "permanently_disabled" if key.permanently_disabled
                    else "active" if key.enabled
                    else "temporarily_disabled"
                ),
                "enabled": key.enabled,
                "permanently_disabled": key.permanently_disabled,
                "recovery_attempted": key.recovery_attempted,
                "weight": key.weight,
                "total_requests": key.total_requests,
                "failed_count": key.failed_count,
                "last_failed_time": key.last_failed_time,
                "created_at": key.created_at,
                "last_used_at": key.last_used_at,
                "description": key.description
            })
        
        return APIResponse(
            success=True,
            message="Keys status information",
            data={
                "summary": stats.dict(),
                "keys": keys_details
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting keys status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get keys status")


@router.post("/keys/reset")
async def reset_key_status(
    request: KeyResetRequest,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Reset the status of a key (clear permanent disable flag).
    
    Args:
        request: Key reset request
        client_key: Validated client API key
        
    Returns:
        API response with reset result
    """
    try:
        success = key_manager.reset_key_status(request.key_suffix)
        
        if success:
            return APIResponse(
                success=True,
                message=f"Key status reset successfully",
                data={"key_suffix": request.key_suffix}
            )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Key with suffix '{request.key_suffix}' not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting key status: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset key status")


@router.post("/keys/{key_suffix}/disable")
async def disable_key(
    key_suffix: str,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Manually disable a key.
    
    Args:
        key_suffix: Suffix of the key to disable
        client_key: Validated client API key
        
    Returns:
        API response with disable result
    """
    try:
        success = key_manager.disable_key(key_suffix)
        
        if success:
            return APIResponse(
                success=True,
                message=f"Key disabled successfully",
                data={"key_suffix": key_suffix}
            )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Key with suffix '{key_suffix}' not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling key: {e}")
        raise HTTPException(status_code=500, detail="Failed to disable key")


@router.post("/keys/{key_suffix}/enable")
async def enable_key(
    key_suffix: str,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Manually enable a key.
    
    Args:
        key_suffix: Suffix of the key to enable
        client_key: Validated client API key
        
    Returns:
        API response with enable result
    """
    try:
        success = key_manager.enable_key(key_suffix)
        
        if success:
            return APIResponse(
                success=True,
                message=f"Key enabled successfully",
                data={"key_suffix": key_suffix}
            )
        elif not key_manager.get_upstream_key_by_suffix(key_suffix):
            raise HTTPException(
                status_code=404,
                detail=f"Key with suffix '{key_suffix}' not found"
            )
        else:
            return APIResponse(
                success=False,
                message=f"Key is permanently disabled and cannot be enabled",
                data={"key_suffix": key_suffix}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error enabling key: {e}")
        raise HTTPException(status_code=500, detail="Failed to enable key")


@router.get("/health/detailed")
async def get_detailed_health(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Get detailed health information including key health details.
    
    Args:
        client_key: Validated client API key
        
    Returns:
        API response with detailed health information
    """
    try:
        # Get health monitor if available
        health_summary = {}
        key_health_details = []
        
        if hasattr(key_manager, '_health_monitor') and key_manager._health_monitor:
            health_summary = key_manager._health_monitor.get_health_summary()
            key_health_details = key_manager._health_monitor.get_key_health_details()
        
        stats = key_manager.get_statistics()
        
        return APIResponse(
            success=True,
            message="Detailed health information",
            data={
                "health_summary": health_summary,
                "key_statistics": stats.dict(),
                "key_health_details": key_health_details,
                "load_balancer": {
                    "current_strategy": key_manager.get_load_balancer_strategy(),
                    "available_strategies": key_manager.get_available_strategies()
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting detailed health: {e}")
        raise HTTPException(status_code=500, detail="Failed to get detailed health information")


@router.post("/health/force-check")
async def force_health_check(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Force an immediate health check and recovery attempt.

    Args:
        client_key: Validated client API key

    Returns:
        API response with health check results
    """
    try:
        if hasattr(key_manager, '_health_monitor') and key_manager._health_monitor:
            results = await key_manager._health_monitor.force_recovery_check()

            return APIResponse(
                success=True,
                message="Forced health check completed",
                data=results
            )
        else:
            return APIResponse(
                success=False,
                message="Health monitoring not available"
            )

    except Exception as e:
        logger.error(f"Error forcing health check: {e}")
        raise HTTPException(status_code=500, detail="Failed to perform health check")


@router.post("/keys/register")
async def register_new_keys(
    request: AutoRegistrationRequest,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Manually trigger registration of new keys.

    Args:
        request: Auto registration request
        client_key: Validated client API key

    Returns:
        API response with registration results
    """
    try:
        if not hasattr(key_manager, '_health_monitor') or not key_manager._health_monitor:
            raise HTTPException(status_code=503, detail="Health monitoring not available")

        health_monitor = key_manager._health_monitor

        if not health_monitor.enable_auto_registration or not health_monitor.auto_registrar:
            raise HTTPException(status_code=400, detail="Auto registration is disabled")

        logger.info(f"Manual registration requested for {request.count} keys")

        new_keys = await health_monitor.auto_registrar.register_multiple_keys(request.count)

        if new_keys:
            # Add new keys to configuration
            config = key_manager.config
            config.upstream_keys.extend(new_keys)
            key_manager.save_config()

            return APIResponse(
                success=True,
                message=f"Successfully registered {len(new_keys)} new keys",
                data={
                    "registered_count": len(new_keys),
                    "requested_count": request.count,
                    "new_keys": [
                        {
                            "key_suffix": key.masked_key,
                            "description": key.description,
                            "weight": key.weight
                        }
                        for key in new_keys
                    ]
                }
            )
        else:
            return APIResponse(
                success=False,
                message="Failed to register any new keys",
                data={"registered_count": 0, "requested_count": request.count}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during manual key registration: {e}")
        raise HTTPException(status_code=500, detail="Failed to register new keys")


@router.get("/keys/auto-registration/config")
async def get_auto_registration_config(
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Get current auto registration configuration.

    Args:
        client_key: Validated client API key

    Returns:
        API response with auto registration configuration
    """
    try:
        from ..config import settings

        config_data = {
            "enable_auto_registration": settings.enable_auto_registration,
            "min_active_keys": settings.min_active_keys,
            "max_registration_attempts": settings.max_registration_attempts
        }

        # Add runtime status
        if hasattr(key_manager, '_health_monitor') and key_manager._health_monitor:
            health_monitor = key_manager._health_monitor
            config_data.update({
                "runtime_enabled": health_monitor.enable_auto_registration,
                "runtime_min_keys": health_monitor.min_active_keys,
                "registrar_available": health_monitor.auto_registrar is not None
            })

        return APIResponse(
            success=True,
            message="Auto registration configuration",
            data=config_data
        )

    except Exception as e:
        logger.error(f"Error getting auto registration config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get configuration")


@router.post("/keys/auto-registration/config")
async def update_auto_registration_config(
    request: AutoRegistrationConfigRequest,
    client_key: str = Depends(verify_client_api_key)
) -> APIResponse:
    """
    Update auto registration configuration at runtime.

    Args:
        request: Auto registration configuration request
        client_key: Validated client API key

    Returns:
        API response with update results
    """
    try:
        if not hasattr(key_manager, '_health_monitor') or not key_manager._health_monitor:
            raise HTTPException(status_code=503, detail="Health monitoring not available")

        health_monitor = key_manager._health_monitor

        # Update runtime configuration
        old_config = {
            "enable_auto_registration": health_monitor.enable_auto_registration,
            "min_active_keys": health_monitor.min_active_keys
        }

        health_monitor.enable_auto_registration = request.enable_auto_registration
        health_monitor.min_active_keys = request.min_active_keys

        # Update auto registrar if needed
        if request.enable_auto_registration and not health_monitor.auto_registrar:
            from ..key_manager.auto_registrar import AutoRegistrar
            health_monitor.auto_registrar = AutoRegistrar(
                min_keys=request.min_active_keys,
                max_registration_attempts=request.max_registration_attempts
            )
        elif health_monitor.auto_registrar:
            health_monitor.auto_registrar.min_keys = request.min_active_keys
            health_monitor.auto_registrar.max_registration_attempts = request.max_registration_attempts

        logger.info(f"Auto registration config updated: {old_config} -> {request.dict()}")

        return APIResponse(
            success=True,
            message="Auto registration configuration updated",
            data={
                "old_config": old_config,
                "new_config": request.dict()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating auto registration config: {e}")
        raise HTTPException(status_code=500, detail="Failed to update configuration")
