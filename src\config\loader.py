"""
Configuration file loader and manager.

This module handles loading and saving configuration files,
with proper error handling and validation.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, TypeVar

from pydantic import BaseModel, ValidationError

from ..models import AnthropicModel, KeysConfig
from .settings import settings

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


class ConfigurationError(Exception):
    """Raised when there's an error with configuration loading or validation."""
    pass


class ConfigLoader:
    """
    Configuration file loader with validation and error handling.
    
    This class provides methods to load and save various configuration files
    used by the application, with proper validation using Pydantic models.
    """
    
    @staticmethod
    def load_json_file(file_path: Path) -> Dict[str, Any]:
        """
        Load a JSON file and return its contents.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Dictionary containing the JSON data
            
        Raises:
            ConfigurationError: If the file cannot be loaded or parsed
        """
        try:
            if not file_path.exists():
                raise ConfigurationError(f"Configuration file not found: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            logger.info(f"Successfully loaded configuration from {file_path}")
            return data
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in {file_path}: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading {file_path}: {e}")
    
    @staticmethod
    def save_json_file(file_path: Path, data: Dict[str, Any]) -> None:
        """
        Save data to a JSON file.
        
        Args:
            file_path: Path to save the JSON file
            data: Data to save
            
        Raises:
            ConfigurationError: If the file cannot be saved
        """
        try:
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, default=str, ensure_ascii=False)
                
            logger.info(f"Successfully saved configuration to {file_path}")
            
        except Exception as e:
            raise ConfigurationError(f"Error saving {file_path}: {e}")
    
    @staticmethod
    def load_model_from_file(file_path: Path, model_class: Type[T]) -> T:
        """
        Load and validate a Pydantic model from a JSON file.
        
        Args:
            file_path: Path to the JSON file
            model_class: Pydantic model class to validate against
            
        Returns:
            Validated model instance
            
        Raises:
            ConfigurationError: If the file cannot be loaded or validation fails
        """
        try:
            data = ConfigLoader.load_json_file(file_path)
            model = model_class(**data)
            logger.info(f"Successfully validated {model_class.__name__} from {file_path}")
            return model
            
        except ValidationError as e:
            raise ConfigurationError(f"Validation error in {file_path}: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading model from {file_path}: {e}")
    
    @staticmethod
    def save_model_to_file(file_path: Path, model: BaseModel) -> None:
        """
        Save a Pydantic model to a JSON file.
        
        Args:
            file_path: Path to save the JSON file
            model: Pydantic model instance to save
            
        Raises:
            ConfigurationError: If the file cannot be saved
        """
        try:
            data = model.dict()
            ConfigLoader.save_json_file(file_path, data)
            logger.info(f"Successfully saved {type(model).__name__} to {file_path}")
            
        except Exception as e:
            raise ConfigurationError(f"Error saving model to {file_path}: {e}")


class KeysConfigLoader:
    """Specialized loader for keys configuration."""
    
    @staticmethod
    def load() -> KeysConfig:
        """
        Load the keys configuration from file.
        
        Returns:
            KeysConfig instance
            
        Raises:
            ConfigurationError: If the configuration cannot be loaded
        """
        file_path = settings.keys_config_path
        
        try:
            return ConfigLoader.load_model_from_file(file_path, KeysConfig)
        except ConfigurationError:
            # If file doesn't exist or is invalid, create a default configuration
            logger.warning(f"Could not load keys config from {file_path}, creating default")
            return KeysConfigLoader.create_default()
    
    @staticmethod
    def save(config: KeysConfig) -> None:
        """
        Save the keys configuration to file.
        
        Args:
            config: KeysConfig instance to save
            
        Raises:
            ConfigurationError: If the configuration cannot be saved
        """
        file_path = settings.keys_config_path
        config.update_timestamp()
        ConfigLoader.save_model_to_file(file_path, config)
    
    @staticmethod
    def create_default() -> KeysConfig:
        """
        Create a default keys configuration.
        
        Returns:
            Default KeysConfig instance
        """
        return KeysConfig(
            upstream_keys=[],
            client_keys=[]
        )


class ModelsConfigLoader:
    """Specialized loader for models configuration."""
    
    @staticmethod
    def load() -> List[AnthropicModel]:
        """
        Load the models configuration from file.
        
        Returns:
            List of AnthropicModel instances
            
        Raises:
            ConfigurationError: If the configuration cannot be loaded
        """
        file_path = settings.models_config_path
        
        try:
            data = ConfigLoader.load_json_file(file_path)
            
            # Validate that data is a list
            if not isinstance(data, list):
                raise ConfigurationError(f"Models config must be a list, got {type(data)}")
            
            # Validate each model
            models = []
            for i, model_data in enumerate(data):
                try:
                    model = AnthropicModel(**model_data)
                    models.append(model)
                except ValidationError as e:
                    logger.warning(f"Invalid model at index {i} in {file_path}: {e}")
                    continue
            
            logger.info(f"Successfully loaded {len(models)} models from {file_path}")
            return models
            
        except ConfigurationError:
            # If file doesn't exist or is invalid, return default models
            logger.warning(f"Could not load models config from {file_path}, using defaults")
            return ModelsConfigLoader.get_default_models()
    
    @staticmethod
    def save(models: List[AnthropicModel]) -> None:
        """
        Save the models configuration to file.
        
        Args:
            models: List of AnthropicModel instances to save
            
        Raises:
            ConfigurationError: If the configuration cannot be saved
        """
        file_path = settings.models_config_path
        data = [model.dict() for model in models]
        ConfigLoader.save_json_file(file_path, data)
    
    @staticmethod
    def get_default_models() -> List[AnthropicModel]:
        """
        Get default models list.
        
        Returns:
            List of default AnthropicModel instances
        """
        return [
            AnthropicModel(
                id="claude-3-7-sonnet-20250219",
                object="model",
                owned_by="anthropic"
            ),
            AnthropicModel(
                id="claude-3-7-sonnet-20250219-thinking",
                object="model",
                owned_by="anthropic"
            ),
            AnthropicModel(
                id="claude-sonnet-4-20250514",
                object="model",
                owned_by="anthropic"
            ),
            AnthropicModel(
                id="claude-sonnet-4-20250514-thinking",
                object="model",
                owned_by="anthropic"
            ),
        ]


# Convenience functions for common operations
def load_keys_config() -> KeysConfig:
    """Load keys configuration."""
    return KeysConfigLoader.load()


def save_keys_config(config: KeysConfig) -> None:
    """Save keys configuration."""
    KeysConfigLoader.save(config)


def load_models_config() -> List[AnthropicModel]:
    """Load models configuration."""
    return ModelsConfigLoader.load()


def save_models_config(models: List[AnthropicModel]) -> None:
    """Save models configuration."""
    ModelsConfigLoader.save(models)
