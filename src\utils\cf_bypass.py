"""
Cloudflare bypass utilities for automated browser operations.
"""

import asyncio
import random
import time
from typing import Optional

from playwright.async_api import Page, <PERSON><PERSON><PERSON>, BrowserContext

from . import get_logger

logger = get_logger(__name__)


class CloudflareBypass:
    """
    Cloudflare bypass utility using various techniques to avoid detection.
    """
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    async def create_stealth_browser(self, playwright, headless: bool = True) -> Browser:
        """
        Create a browser with stealth configurations to bypass Cloudflare.
        """
        logger.info("Creating stealth browser for Cloudflare bypass...")
        
        # Enhanced browser arguments for stealth mode
        args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-back-forward-cache',
            '--disable-background-networking',
            '--disable-breakpad',
            '--disable-client-side-phishing-detection',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--disable-features=TranslateUI',
            '--disable-hang-monitor',
            '--disable-ipc-flooding-protection',
            '--disable-popup-blocking',
            '--disable-prompt-on-repost',
            '--disable-sync',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--no-default-browser-check',
            '--password-store=basic',
            '--use-mock-keychain',
            '--disable-blink-features=AutomationControlled'
        ]
        
        browser = await playwright.chromium.launch(
            headless=headless,
            args=args,
            slow_mo=random.randint(50, 150)  # Random delay between actions
        )
        
        return browser
    
    async def create_stealth_context(self, browser: Browser) -> BrowserContext:
        """
        Create a browser context with stealth configurations.
        """
        logger.info("Creating stealth browser context...")
        
        # Random user agent
        user_agent = random.choice(self.user_agents)
        
        # Random viewport size
        viewports = [
            {'width': 1920, 'height': 1080},
            {'width': 1366, 'height': 768},
            {'width': 1440, 'height': 900},
            {'width': 1536, 'height': 864}
        ]
        viewport = random.choice(viewports)
        
        context = await browser.new_context(
            user_agent=user_agent,
            viewport=viewport,
            locale='en-US',
            timezone_id='America/New_York',
            permissions=['geolocation'],
            geolocation={'latitude': 40.7128, 'longitude': -74.0060},  # New York
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
        )
        
        return context
    
    async def setup_stealth_page(self, page: Page) -> None:
        """
        Setup page with stealth scripts to avoid detection.
        """
        logger.info("Setting up stealth page configurations...")
        
        # Remove webdriver property
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        # Override plugins
        await page.add_init_script("""
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
        """)
        
        # Override languages
        await page.add_init_script("""
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        # Override permissions
        await page.add_init_script("""
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        # Random mouse movements
        await self.add_random_mouse_movements(page)
    
    async def add_random_mouse_movements(self, page: Page) -> None:
        """
        Add random mouse movements to simulate human behavior.
        """
        await page.add_init_script("""
            // Add random mouse movements
            setInterval(() => {
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                const event = new MouseEvent('mousemove', {
                    clientX: x,
                    clientY: y
                });
                document.dispatchEvent(event);
            }, Math.random() * 5000 + 1000);
        """)
    
    async def navigate_with_retry(self, page: Page, url: str, max_retries: int = 3) -> bool:
        """
        Navigate to URL with Cloudflare bypass and retry logic.
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting to navigate to {url} (attempt {attempt + 1}/{max_retries})")
                
                # Random delay before navigation
                await asyncio.sleep(random.uniform(1, 3))
                
                response = await page.goto(
                    url,
                    timeout=90000,
                    wait_until="domcontentloaded"
                )
                
                logger.info(f"Navigation response status: {response.status}")
                
                # Wait for page to load
                await asyncio.sleep(random.uniform(3, 6))
                
                # Check if we hit Cloudflare challenge
                if await self.is_cloudflare_challenge(page):
                    logger.warning("Cloudflare challenge detected, waiting for bypass...")
                    if await self.wait_for_cloudflare_bypass(page):
                        logger.info("Cloudflare challenge bypassed successfully")
                        return True
                    else:
                        logger.warning("Failed to bypass Cloudflare challenge")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(random.uniform(5, 10))
                            continue
                        return False
                else:
                    logger.info("No Cloudflare challenge detected, navigation successful")
                    return True
                    
            except Exception as e:
                logger.warning(f"Navigation attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(5, 10))
                else:
                    return False
        
        return False
    
    async def is_cloudflare_challenge(self, page: Page) -> bool:
        """
        Check if the current page is a Cloudflare challenge.
        """
        try:
            # Check for common Cloudflare challenge indicators
            cf_indicators = [
                'cf-browser-verification',
                'cf-challenge-running',
                'cf-challenge-form',
                'Checking your browser',
                'DDoS protection by Cloudflare',
                'Ray ID'
            ]
            
            page_content = await page.content()
            title = await page.title()
            
            for indicator in cf_indicators:
                if indicator.lower() in page_content.lower() or indicator.lower() in title.lower():
                    return True
            
            # Check for Cloudflare challenge elements
            challenge_selectors = [
                '#cf-challenge-running',
                '.cf-browser-verification',
                '[data-ray]'
            ]
            
            for selector in challenge_selectors:
                if await page.locator(selector).count() > 0:
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Error checking for Cloudflare challenge: {e}")
            return False
    
    async def wait_for_cloudflare_bypass(self, page: Page, timeout: int = 30) -> bool:
        """
        Wait for Cloudflare challenge to be bypassed.
        """
        try:
            logger.info("Waiting for Cloudflare challenge to be bypassed...")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                if not await self.is_cloudflare_challenge(page):
                    logger.info("Cloudflare challenge bypassed!")
                    return True
                
                await asyncio.sleep(1)
            
            logger.warning("Timeout waiting for Cloudflare bypass")
            return False
            
        except Exception as e:
            logger.error(f"Error waiting for Cloudflare bypass: {e}")
            return False
    
    async def human_like_typing(self, page: Page, selector: str, text: str) -> None:
        """
        Type text in a human-like manner with random delays.
        """
        element = page.locator(selector)
        await element.click()
        await asyncio.sleep(random.uniform(0.1, 0.3))
        
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.05, 0.15))
        
        await asyncio.sleep(random.uniform(0.2, 0.5))
    
    async def human_like_click(self, page: Page, selector: str) -> None:
        """
        Click element in a human-like manner.
        """
        element = page.locator(selector)
        
        # Random delay before click
        await asyncio.sleep(random.uniform(0.5, 1.5))
        
        # Move to element first
        await element.hover()
        await asyncio.sleep(random.uniform(0.1, 0.3))
        
        # Click
        await element.click()
        
        # Random delay after click
        await asyncio.sleep(random.uniform(0.3, 0.8))
