"""
Load balancer strategy manager.

This module provides a centralized manager for load balancing strategies,
allowing easy registration, selection, and switching between strategies.
"""

import logging
from typing import Dict, List, Optional, Type

from ..models import LoadBalancerStrategy as LoadBalancerStrategyEnum
from ..models import UpstreamKey
from .base import LoadBalancerStrategy
from .strategies import (
    LeastFailedStrategy,
    LeastUsedStrategy,
    RandomStrategy,
    RoundRobinStrategy,
    WeightedRandomStrategy,
    WeightedRoundRobinStrategy,
)

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Manager for load balancing strategies.
    
    This class provides a registry of available strategies and methods
    to select and use them for key selection.
    """
    
    def __init__(self):
        """Initialize the strategy manager with default strategies."""
        self._strategies: Dict[str, LoadBalancerStrategy] = {}
        self._current_strategy_name: str = "round_robin"
        
        # Register default strategies
        self._register_default_strategies()
    
    def _register_default_strategies(self) -> None:
        """Register all default load balancing strategies."""
        default_strategies = [
            RoundRobinStrategy(),
            WeightedRoundRobinStrategy(),
            RandomStrategy(),
            WeightedRandomStrategy(),
            LeastUsedStrategy(),
            LeastFailedStrategy(),
        ]
        
        for strategy in default_strategies:
            self.register_strategy(strategy)
    
    def register_strategy(self, strategy: LoadBalancerStrategy) -> None:
        """
        Register a new load balancing strategy.
        
        Args:
            strategy: Strategy instance to register
        """
        self._strategies[strategy.name] = strategy
        logger.info(f"Registered load balancing strategy: {strategy.name}")
    
    def unregister_strategy(self, strategy_name: str) -> None:
        """
        Unregister a load balancing strategy.
        
        Args:
            strategy_name: Name of the strategy to unregister
            
        Raises:
            ValueError: If strategy is not found or is the current strategy
        """
        if strategy_name not in self._strategies:
            raise ValueError(f"Strategy '{strategy_name}' is not registered")
        
        if strategy_name == self._current_strategy_name:
            raise ValueError("Cannot unregister the current strategy")
        
        del self._strategies[strategy_name]
        logger.info(f"Unregistered load balancing strategy: {strategy_name}")
    
    def get_strategy(self, strategy_name: str) -> LoadBalancerStrategy:
        """
        Get a strategy by name.
        
        Args:
            strategy_name: Name of the strategy to get
            
        Returns:
            Strategy instance
            
        Raises:
            ValueError: If strategy is not found
        """
        if strategy_name not in self._strategies:
            raise ValueError(f"Strategy '{strategy_name}' is not registered")
        
        return self._strategies[strategy_name]
    
    def list_strategies(self) -> List[str]:
        """
        Get a list of all registered strategy names.
        
        Returns:
            List of strategy names
        """
        return list(self._strategies.keys())
    
    def get_strategy_info(self, strategy_name: str) -> Dict[str, str]:
        """
        Get information about a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            Dictionary with strategy information
            
        Raises:
            ValueError: If strategy is not found
        """
        strategy = self.get_strategy(strategy_name)
        return {
            "name": strategy.name,
            "description": strategy.description,
            "type": type(strategy).__name__,
            "is_stateful": hasattr(strategy, '_state'),
        }
    
    def set_current_strategy(self, strategy_name: str) -> None:
        """
        Set the current active strategy.
        
        Args:
            strategy_name: Name of the strategy to set as current
            
        Raises:
            ValueError: If strategy is not found
        """
        if strategy_name not in self._strategies:
            raise ValueError(f"Strategy '{strategy_name}' is not registered")
        
        old_strategy = self._current_strategy_name
        self._current_strategy_name = strategy_name
        
        logger.info(f"Changed load balancing strategy from '{old_strategy}' to '{strategy_name}'")
    
    def get_current_strategy(self) -> LoadBalancerStrategy:
        """
        Get the current active strategy.
        
        Returns:
            Current strategy instance
        """
        return self._strategies[self._current_strategy_name]
    
    def get_current_strategy_name(self) -> str:
        """
        Get the name of the current active strategy.
        
        Returns:
            Current strategy name
        """
        return self._current_strategy_name
    
    def select_key(self, keys: List[UpstreamKey], strategy_name: Optional[str] = None) -> UpstreamKey:
        """
        Select a key using the specified or current strategy.
        
        Args:
            keys: List of available upstream keys
            strategy_name: Optional strategy name to use (defaults to current)
            
        Returns:
            Selected UpstreamKey
            
        Raises:
            ValueError: If strategy is not found or no keys are available
        """
        if strategy_name is None:
            strategy = self.get_current_strategy()
        else:
            strategy = self.get_strategy(strategy_name)
        
        selected_key = strategy.select_key(keys)
        
        logger.debug(
            f"Selected key {selected_key.masked_key} using strategy '{strategy.name}'"
        )
        
        return selected_key
    
    def reset_strategy_state(self, strategy_name: Optional[str] = None) -> None:
        """
        Reset the state of a strategy (if it's stateful).
        
        Args:
            strategy_name: Optional strategy name (defaults to current)
        """
        if strategy_name is None:
            strategy = self.get_current_strategy()
        else:
            strategy = self.get_strategy(strategy_name)
        
        if hasattr(strategy, 'reset_state'):
            strategy.reset_state()
            logger.info(f"Reset state for strategy '{strategy.name}'")
    
    def get_strategy_state(self, strategy_name: Optional[str] = None) -> Optional[dict]:
        """
        Get the state of a strategy (if it's stateful).
        
        Args:
            strategy_name: Optional strategy name (defaults to current)
            
        Returns:
            Strategy state or None if strategy is stateless
        """
        if strategy_name is None:
            strategy = self.get_current_strategy()
        else:
            strategy = self.get_strategy(strategy_name)
        
        if hasattr(strategy, 'get_state'):
            return strategy.get_state()
        
        return None
    
    def validate_strategy_name(self, strategy_name: str) -> bool:
        """
        Validate that a strategy name is registered.
        
        Args:
            strategy_name: Strategy name to validate
            
        Returns:
            True if strategy is registered, False otherwise
        """
        return strategy_name in self._strategies


# Global strategy manager instance
strategy_manager = StrategyManager()
